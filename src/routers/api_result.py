# api_result.py
# 一个经过优化、更健壮的非阻塞管理器，用于处理来自异步工作进程的同步式结果。

import asyncio
import json
import time
from contextlib import suppress
from typing import Any

import redis
import redis.asyncio as aioredis
from fastapi import FastAPI

# --- 导入共享配置 ---
# 假设您的配置文件路径是 'src.conf.config'
from src.conf.config import CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)

# --- 常量定义 ---
ZOMBIE_TASK_CLEANUP_INTERVAL_SECONDS = 300  # 每 5 分钟清理一次僵尸任务
ZOMBIE_TASK_LIFETIME_SECONDS = 360  # 任务存活超过 6 分钟即被视为僵尸


class ResultManager:
    """
    管理需要同步返回结果的任务。

    通过 Redis Pub/Sub 监听 worker 的结果，并使用 asyncio.Event
    唤醒等待特定任务结果的 API 请求处理协程。
    这是一个经过优化的版本，具有更好的健壮性和资源管理。
    """

    def __init__(self):
        """
        初始化管理器。
        _pending_tasks 字典的结构变为: {task_id: (event, result, creation_time)}
        增加了创建时间戳，用于清理僵尸任务。
        """
        self._pending_tasks: dict[str, tuple[asyncio.Event, Any, float]] = {}
        self.redis_client: aioredis.Redis | None = None
        self._listener_task: asyncio.Task | None = None
        self._cleanup_task: asyncio.Task | None = None

    async def initialize(self):
        """
        连接到 Redis 并启动所有后台协程（结果监听器和僵尸任务清理器）。
        """
        if self.redis_client:
            return

        self.redis_client = aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
        await self.redis_client.ping()
        logger.info("结果管理器成功连接到 Redis。")

        self._listener_task = asyncio.create_task(self._result_listener())
        self._cleanup_task = asyncio.create_task(self._zombie_task_cleanup())
        logger.info("后台结果监听器和僵尸任务清理器已启动。")

    async def _result_listener(self):
        """
        长期运行的后台协程，负责监听 Redis Pub/Sub 频道。
        优化了重连逻辑，只有在连接断开时才尝试重新订阅。
        """
        while True:
            try:
                pubsub = self.redis_client.pubsub()
                await pubsub.subscribe(f"{CONFIG.Redis.RESULT_CHANNEL_PREFIX}{CONFIG.worker_ip}")
                logger.info(f"已成功订阅结果频道: '{CONFIG.Redis.RESULT_CHANNEL_PREFIX}{CONFIG.worker_ip}'")

                async for message in pubsub.listen():
                    if message["type"] != "message":
                        continue

                    # 使用 suppress 上下文管理器使代码更简洁
                    with suppress(json.JSONDecodeError, TypeError):
                        result_data = json.loads(message["data"])
                        task_id = result_data.get("id")

                        if task_id and task_id in self._pending_tasks:
                            logger.info(f"收到任务 '{task_id}' 的结果。")
                            event, _, creation_time = self._pending_tasks[task_id]
                            self._pending_tasks[task_id] = (event, result_data, creation_time)
                            event.set()

            except redis.exceptions.ConnectionError as e:
                logger.error(f"Redis 连接丢失: {e}。将在5秒后重试...")
                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"结果监听器出现未知严重错误: {e}。将在5秒后重试...")
                await asyncio.sleep(5)

    async def _zombie_task_cleanup(self):
        """
        一个后台守护协程，定期清理因未知原因残留的僵尸任务，防止内存泄漏。
        """
        while True:
            await asyncio.sleep(ZOMBIE_TASK_CLEANUP_INTERVAL_SECONDS)

            now = time.time()
            zombie_tasks = [
                task_id for task_id, (_, _, creation_time) in self._pending_tasks.items()
                if now - creation_time > ZOMBIE_TASK_LIFETIME_SECONDS
            ]

            if zombie_tasks:
                logger.warning(f"检测到 {len(zombie_tasks)} 个僵尸任务，正在清理: {zombie_tasks}")
                for task_id in zombie_tasks:
                    # 使用 pop 安全地移除
                    self._pending_tasks.pop(task_id, None)

    async def wait_for_result(self, task_id: str, timeout: int = 60) -> Any:
        """
        API 端点调用的主方法。它为给定的 task_id 等待结果。
        增加了对 asyncio.CancelledError 的处理。
        """
        event = asyncio.Event()
        self._pending_tasks[task_id] = (event, None, time.time())  # 注册时记录时间戳

        try:
            await asyncio.wait_for(event.wait(), timeout=timeout)
            _, result, _ = self._pending_tasks[task_id]
            return result
        except asyncio.TimeoutError:
            logger.warning(f"等待任务 '{task_id}' 的结果超时。")
            return {"status": "error", "id": task_id, "message": f"Request timed out after {timeout} seconds."}
        except asyncio.CancelledError:
            # 当客户端断开连接时，FastAPI 会触发此异常
            logger.info(f"任务 '{task_id}' 的等待被客户端取消。")
            # 必须重新抛出，以便 FastAPI 正确处理连接关闭
            raise
        finally:
            # 无论成功、超时或取消，都清理任务
            self._pending_tasks.pop(task_id, None)
            logger.debug(f"已清理任务 '{task_id}' 的等待状态。")

    async def shutdown(self):
        """
        在应用关闭时，优雅地取消所有后台任务并关闭 Redis 连接。
        """
        if self._listener_task:
            self._listener_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()

        # 等待任务真正被取消
        with suppress(asyncio.CancelledError):
            await asyncio.gather(self._listener_task, self._cleanup_task, return_exceptions=True)

        if self.redis_client:
            await self.redis_client.close()
        logger.info("结果管理器已成功关闭并清理了所有资源。")


# --- 单例实例和 FastAPI 事件集成 ---
result_manager = ResultManager()


def setup_result_manager(app: FastAPI):
    @app.on_event("startup")
    async def startup_event():
        await result_manager.initialize()

    @app.on_event("shutdown")
    async def shutdown_event():
        await result_manager.shutdown()


def get_result_manager() -> ResultManager:
    return result_manager