# -*- coding: utf-8 -*-
import asyncio
import json
import time
from datetime import datetime
from typing import Any

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel

from src.conf.config import CONFIG
from src.core.browser_manager import load_cookies_from_file
from src.core.task_dispatcher import dispatch_sync, dispatch_async
from src.utils.cache_manager import cache_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/agent",
    tags=["agent api"]
)


# --- Pydantic Models ---
class ApiResponse(BaseModel):
    timestamp: str
    code: int
    message: str
    result: Any | None = None


class ResumeDetailRequest(BaseModel):
    resume_detail_url: str

class WeeklyReportRecruitDataCenterGetItem(BaseModel):
    bnLoginName: str
    recmtUserName: str
    date: str

# --- Helper Functions ---
def recode_request_info(request: Request):
    logger.info(f"client ip: {request.client.host}, request uri: {request.url.path}")


def cookie_check(account_name: str = "") -> bool:
    cookies = load_cookies_from_file(account_name)
    return bool(cookies and cookies.get('wt2'))


async def check_worker_liveness() -> tuple[bool, str, dict]:
    import redis.asyncio as aioredis
    async_redis_client = aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
    try:
        raw = await async_redis_client.get(f"{CONFIG.Redis.STATUS_KEY_PREFIX}{CONFIG.worker_ip}")
        if not raw:
            return False, "No active worker found (status key does not exist).", {}

        worker_status = json.loads(raw)
        last_update_str = worker_status.get("last_update_utc")
        if not last_update_str:
            return False, "Worker status is incomplete (missing last_update).", worker_status

        last_update_dt = datetime.fromisoformat(last_update_str)
        time_since_last_update = datetime.now() - last_update_dt

        if time_since_last_update.total_seconds() > CONFIG.Redis.WORKER_HEARTBEAT_TIMEOUT_SECONDS:
            return False, f"Worker is offline. Last heartbeat was {int(time_since_last_update.total_seconds())} seconds ago.", worker_status

        return True, "Worker is alive.", worker_status
    finally:
        await async_redis_client.close()


# --- API Endpoints ---
@router.get("/loginStatus")
def get_login_status(request: Request, recmtUserName: str):
    recode_request_info(request)
    status = "0" if cookie_check(recmtUserName) else "1"
    return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "",
            "result": {"status": status, "version": 1}}


@router.get("/login", response_model=ApiResponse)
async def agent_login(request: Request, recmtUserName: str, bnLoginName: str):
    recode_request_info(request)
    lock_key = f"{CONFIG.Redis.TASK_LOCK_PREFIX}login:{recmtUserName}:{bnLoginName}"
    payload = {"account_name": recmtUserName, "bn_login_name": bnLoginName}

    try:
        # Dispatch with lock and wait for the result synchronously
        worker_result = await dispatch_sync("login", payload, timeout=300)

        now_ts = str(int(time.time() * 1000))
        if worker_result.get("status") == "success":
            final_data = worker_result.get("data", {})
            final_data["recmtUserName"] = recmtUserName
            final_data["bnLoginName"] = bnLoginName
            final_data["status"] = 1 if final_data.get("login") else 0
            return ApiResponse(timestamp=now_ts, code=0, message="Task completed successfully.", result=final_data)
        else:
            error_message = worker_result.get("message", "Task failed or timed out.")
            return ApiResponse(timestamp=now_ts, code=999, message=error_message, result={"status": "2", "qrcode": ""})
    except HTTPException as e:
        # Re-raise HTTPException from dispatch_async_locked
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=e.status_code, message=e.detail,
                           result={"status": "998"})
    finally:
        # Clean up the lock, regardless of the outcome
        from src.core.task_dispatcher import redis_client
        redis_client.delete(lock_key)


@router.get("/job/allList", response_model=ApiResponse)
async def agent_get_job_list(request: Request, recmtUserName: str):
    recode_request_info(request)
    if not cookie_check(recmtUserName):
        return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "未登录", "result": {"status": "995"}}

    result = await dispatch_sync("get_job_list", {}, timeout=150)

    if result.get("status") == "success":
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=0, message="职位列表获取成功",
                           result=result.get("data", {}).get("result"))
    else:
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=400, message="职位列表获取失败",
                           result=result.get("data", {}).get("result"))

@router.get("/jobfilter/trigger")
async def agent_job_filter_trigger(request: Request, recmtUserName: str, bnLoginName: str, jobId: str, batchNo: str,
                                   filterType: str = '1', interval: str = '1', endTime: str = ''):
    recode_request_info(request)
    if not cookie_check(recmtUserName):
        return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "未登录", "result": {"status": 995}}

    is_alive, message, worker_status = await check_worker_liveness()
    if not is_alive:
        raise HTTPException(status_code=503, detail={"status": "error", "message": message})

    if worker_status.get('status') == 'processing_task':
        if is_alive and worker_status.get("current_task_id") == batchNo:
            return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "",
                    "result": {"status": 0, "batchNo": batchNo, "jobId": jobId, "bnLoginName": bnLoginName}}
        return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "有任务正在运行,请稍后再试",
                "result": {"status": 998}}

    payload = {
        'batchNo': batchNo,
        "jobId": jobId,
        "account_name": recmtUserName,
        "bn_login_name": bnLoginName,
        "filterType": filterType,
        'interval': interval,
        'endTime': endTime
    }

    await dispatch_async("jobFilterTrigger", payload)

    # The original logic had a polling loop. This is generally not ideal.
    # A better approach would be for the client to poll a status endpoint using the returned task_id.
    # However, to maintain functional equivalence, we replicate the polling.
    for _ in range(10):
        await asyncio.sleep(0.5)
        is_alive, _, worker_status = await check_worker_liveness()
        logger.info(f"worker_status: {worker_status}")
        if is_alive and worker_status.get("current_task_id") == batchNo:
            return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "",
                    "result": {"status": 0, "batchNo": batchNo, "jobId": jobId, "bnLoginName": bnLoginName}}
        if worker_status.get('status') == 'paused_on_error':
            return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "服务暂时不可用",
                    "result": {"status": 999}}

    return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "请求超时", "result": {"status": 999}}


@router.get("/listJobs")
async def list_jobs():
    """获取任务列表状态"""
    is_alive, message, worker_status = await check_worker_liveness()

    if not is_alive:
        # 如果worker不可用，返回默认状态
        data = {"node_name": "node", "status": "offline", "pending": 0, "running": 0, "finished": 0}
    else:
        data = {
            "node_name": "node",
            "status": "ok",
            "pending": worker_status.get("pending", 0),
            "running": worker_status.get("running", 0),
            "finished": worker_status.get("finished", 0)
        }

    return {"timestamp": str(int(time.time() * 1000)), "code": 0, "message": "", "result": data}


@router.post("/resume/detail", response_model=ApiResponse)
async def get_resume_detail(request: Request, resume_request: ResumeDetailRequest):
    recode_request_info(request)
    if not resume_request.resume_detail_url:
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=400, message="缺少必要的resume_detail_url参数",
                           result={"status": "error", "message": "缺少必要的resume_detail_url参数"})

    is_alive, message, worker_status = await check_worker_liveness()
    if not is_alive:
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=503, message="Worker服务不可用",
                           result={"status": "error", "message": message})

    if worker_status.get('status') == 'processing_task':
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=409, message="有任务正在运行，请稍后再试",
                           result={"status": "error", "message": "另一个任务正在运行，请稍后再试"})

    worker_result = await dispatch_sync("get_resume_detail", {"url": resume_request.resume_detail_url}, timeout=150)

    now_ts = str(int(time.time() * 1000))
    if worker_result.get("status") == "success":
        return ApiResponse(timestamp=now_ts, code=0, message="简历详情获取成功",
                           result={"status": "success", "data": worker_result.get("data", {}),
                                   "task_id": worker_result.get("id")})
    else:
        error_message = worker_result.get("message", "获取简历详情失败")
        return ApiResponse(timestamp=now_ts, code=500, message=error_message,
                           result={"status": "error", "message": error_message, "task_id": worker_result.get("id")})


@router.post("/weeklyReport/recruitDataCenter/get", response_model=ApiResponse)
async def get_weekly_report(request: Request, item: WeeklyReportRecruitDataCenterGetItem):
    recode_request_info(request)

    # 生成缓存键（基于用户标识）
    cache_key = f"weekly_report_{CONFIG.worker_ip}"

    # 尝试从缓存获取数据
    cached_result = cache_manager.get_cache(cache_key)
    if cached_result is not None:
        now_ts = str(int(time.time() * 1000))
        logger.info(f"周报数据缓存命中: {cache_key}")
        return ApiResponse(
            timestamp=now_ts,
            code=0,
            message="周报数据获取成功（缓存）",
            result=cached_result
        )

    is_alive, message, worker_status = await check_worker_liveness()
    if not is_alive:
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=503, message="Worker服务不可用",
                           result={"status": "error", "message": message})
    if worker_status.get('status') == 'processing_task':
        return ApiResponse(timestamp=str(int(time.time() * 1000)), code=409, message="有任务正在运行，请稍后再试",
                           result={"status": "error", "message": "另一个任务正在运行，请稍后再试"})

    # 从Worker获取新数据
    worker_result = await dispatch_sync("get_weekly_report", {}, timeout=150)

    now_ts = str(int(time.time() * 1000))
    if worker_result.get("status") == "success":
        # 缓存成功结果（30分钟，启用夜间模式）
        cache_manager.set_cache(
            key=cache_key,
            value=worker_result.get("data", {}),
            ttl_minutes=30,
            enable_night_mode=True
        )
        logger.info(f"周报数据已缓存: {cache_key}")
        return ApiResponse(timestamp=now_ts, code=0, message="周报数据获取成功", result=worker_result.get("data", {}))
    else:
        error_message = worker_result.get("message", "获取周报数据失败")
        return ApiResponse(timestamp=now_ts, code=500, message=error_message, result=worker_result.get("data", {}))