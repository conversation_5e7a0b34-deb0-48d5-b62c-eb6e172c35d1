import asyncio
import json
import time
import traceback
from contextlib import suppress
from datetime import datetime

import redis.asyncio as aioredis
from playwright.async_api import async_playwright, Page

from src.conf.config import CONFIG
from src.core.exceptions import TaskException, ErrorCodes
from src.core.task_processor import get_task_processor
from src.monitors import update_worker_status, stop_heartbeat, start_heartbeat
from src.monitors.disk_auto_cleanup import start_auto_cleanup, stop_auto_cleanup
from src.utils.logger import get_logger
from src.utils.mailer import send_monitoring_alert
from src.utils.tracing_manager import start_trace, stop_trace

logger = get_logger(__name__)

# 全局变量，用于跟踪任务运行状态（保持兼容性）
is_task_running = False
heartbeat_task = None  # 心跳任务
current_task_info = None  # 当前执行的任务信息


async def handle_task(page: Page, task: dict, redis: aioredis.Redis):
    global is_task_running, current_task_info
    action = task.get("action")
    task_id = task.get("id", "N/A")
    task_type = action
    account_name = task.get("account_name") or task.get("payload", {}).get("account_name")
    start_time = time.time()

    # 检查是否有任务正在运行
    if is_task_running:
        error_result = {
            "status": "error",
            "id": task_id,
            "message": "另一个任务正在运行，请稍后再试",
            "error_code": ErrorCodes.TASK_CONCURRENT_CONFLICT
        }
        task_result_channel = task.get("result_channel")
        if task_result_channel:
            await redis.publish(task_result_channel, json.dumps(error_result))
        return error_result

    # 设置任务运行状态和当前任务信息
    is_task_running = True
    current_task_info = task.copy()  # 保存当前任务信息供心跳使用

    await update_worker_status(redis, "processing_task", task_info=task)

    logger.info(f"开始处理任务: {task_id} ({task_type})")

    # 启动tracing记录
    await start_trace(page, task_type, account_name)

    if page:
        current_url = page.url
        print(f"当前页面的URL是: {current_url}")
    else:
        raise Exception("无法将页面重置到初始状态，任务中止。")

    processor = get_task_processor(action, page)
    result = await processor.process(task)

    processing_time = time.time() - start_time
    logger.info(f"任务 {task_id} 处理完成，状态: {result.get('status', 'unknown')}, 耗时: {processing_time:.2f}秒")

    await stop_trace(page)

    # 如果任务成功，确保结果包含处理时间
    if result.get("status") == "success" and "processing_time" not in result:
        result["processing_time"] = processing_time

    return result


# --- 管理指令等待循环 ---
async def pause_and_wait_for_admin_command(redis: aioredis.Redis):
    """
    阻塞等待管理指令队列，收到指令后返回。
    支持指令：shutdown、resume、restart、status等。
    """
    queue_name = f"{CONFIG.Redis.ADMIN_QUEUE_PREFIX}{CONFIG.USER_ID}"
    print(f"\n{'=' * 50}\n[管理模式] 阻塞等待管理员指令（队列：{queue_name}）...")

    while True:
        result = await redis.blpop(queue_name, timeout=0)  # 阻塞直到有指令
        if result:
            _, raw_cmd = result
            try:
                # 兼容json字符串
                cmd_obj = json.loads(raw_cmd)
                command = cmd_obj.get("command") or cmd_obj.get("cmd") or raw_cmd
            except Exception:
                command = raw_cmd
            print(f"[管理指令] 收到指令: {command}")
            return command  # 返回指令给主循环
        await asyncio.sleep(0.1)  # 理论上不会走到这里，仅保险


async def _enter_management_mode(redis: aioredis.Redis, playwright_instance, page, initialize_browser):
    """
    进入管理模式，等待管理员指令处理异常情况

    Args:
        redis: Redis连接
        playwright_instance: Playwright实例
        page: 当前页面对象
        initialize_browser: 浏览器初始化函数
    """
    in_management_mode = True
    while in_management_mode:
        command = await pause_and_wait_for_admin_command(redis)

        if command == "shutdown":
            logger.info("[管理指令] 执行关闭...")
            await stop_heartbeat()  # 停止心跳任务
            await playwright_instance.stop()  # 确保 playwright 停止
            return "shutdown"  # 返回特殊值表示需要退出主循环

        elif command == "resume":
            logger.info("[管理指令] 恢复运行...")
            await update_worker_status(redis, "idle")
            in_management_mode = False  # 跳出管理模式，恢复任务循环

        elif command == "restart":
            logger.info("[管理指令] 执行浏览器重启...")
            # 关闭现有浏览器
            if page and page.context.browser.is_connected():
                await page.context.browser.close()
            # 重新初始化浏览器和页面
            new_page = await initialize_browser()
            if not new_page:
                logger.error("重启浏览器失败，程序退出。")
                await stop_heartbeat()  # 停止心跳任务
                await playwright_instance.stop()
                return "shutdown"
            await update_worker_status(redis, "idle")
            in_management_mode = False  # 跳出管理模式，恢复任务循环
            return new_page  # 返回新的页面对象

        elif command == "status":
            logger.info("[管理指令] 查询状态...")
            # 发送当前状态信息
            status_info = f"""
当前Worker状态: 管理模式 (等待指令)
用户ID: {CONFIG.USER_ID}
时间: {datetime.now().isoformat()}
可用指令: shutdown, resume, restart, status
"""
            await send_monitoring_alert(
                title=f"SRA Worker 状态查询 - {CONFIG.USER_ID}",
                error_details=status_info,
                page=page,
                severity="info"
            )
        else:
            logger.warning(f"[管理指令] 未知指令: {command}")

    return None  # 正常恢复


async def _get_task_error_details(e, account_name, page, is_task=True):
    url = page.url if page else "未知"
    if is_task:
        return f"""
SRA Worker ({account_name}) 在执行任务时遇到已知异常并已暂停，需要人工干预。

时间: {datetime.now().isoformat()}
异常类型: TaskException
错误代码: {getattr(e, 'error_code', None)}
错误信息: {getattr(e, 'message', str(e))}

最后访问的URL: {url}

异常详情:
{getattr(e, 'details', '')}

详细堆栈跟踪:
{traceback.format_exc()}"""
    else:
        login_expired = False
        if page and 'https://www.zhipin.com/web/user/?ka=' in url:
            login_expired = True
        return f"""
SRA Worker ({account_name}) 在执行任务时遇到严重未知异常并已暂停，需要人工干预。

时间: {datetime.now().isoformat()}
错误类型: {type(e).__name__}
错误信息: {str(e)}
登录状态: {'已失效' if login_expired else '正常'}

最后访问的URL: {url}

详细堆栈跟踪:
{traceback.format_exc()}"""

async def handle_exception_and_enter_management_mode(e, task, page, redis, playwright_instance, initialize_browser, is_task_exception=True):
    import os
    account_name = os.environ.get('account_name')
    error_details = await _get_task_error_details(e, account_name, page, is_task=is_task_exception)
    severity = "error" if is_task_exception else "critical"
    title = f"SRA Worker 任务异常 - {CONFIG.USER_ID}" if is_task_exception else f"SRA Worker 严重异常 - {CONFIG.USER_ID}"
    # 发送监控告警
    await send_monitoring_alert(
        title=title,
        error_details=error_details,
        page=page,
        severity=severity
    )
    await update_worker_status(redis, "paused_on_error")
    logger.info(("任务异常" if is_task_exception else "严重异常") + "，进入管理模式，等待管理员指令...")
    management_result = await _enter_management_mode(redis, playwright_instance, page, initialize_browser)
    if management_result == "shutdown":
        return None, "shutdown", page
    elif isinstance(management_result, Page):
        return None, "restart", management_result
    # 构造统一的 result
    result = {
        "status": "error",
        "id": task.get("id") if task else None,
        "message": getattr(e, "message", str(e)),
        "error_code": getattr(e, "error_code", None)
    }
    return result, None, page


# --- 主管理循环 (修改了 init_driver 的调用) ---
async def browser_manager():
    redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
    playwright_instance = await async_playwright().start()
    page: Page = None

    async def initialize_browser():
        nonlocal page
        if page and page.context.browser.is_connected():
            await page.context.browser.close()

        await update_worker_status(redis, "initializing")
        print("正在初始化新浏览器...")
        try:
            # init_driver
            from src.core.browser_manager import init_driver
            page = await init_driver()
            print("浏览器及页面已成功创建。")
            return page
        except Exception as e:
            print(f"初始化浏览器失败: {e}")
            await update_worker_status(redis, "failed_to_start")
            return None

    page = await initialize_browser()
    if not page:
        print("初始浏览器创建失败，程序退出。")
        await playwright_instance.stop()
        return

    await update_worker_status(redis, "idle")

    # 启动心跳任务
    await start_heartbeat(redis, interval=30)  # 30秒间隔更新心跳

    # 主事件循环
    while True:
        task = None
        result = None
        try:
            print(f"\n等待来自任务队列 '{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.USER_ID}' 的新任务...")
            _, raw = await redis.blpop(f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.USER_ID}")
            task = json.loads(raw)
            await update_worker_status(redis, "pending", task_info=task)
            result = await handle_task(page, task, redis)
            await update_worker_status(redis, "finished", task_info=task)
        except TaskException as e:
            result, control, page = await handle_exception_and_enter_management_mode(
                e, task, page, redis, playwright_instance, initialize_browser, is_task_exception=True)
            if control == "shutdown":
                return
        except Exception as e:
            result, control, page = await handle_exception_and_enter_management_mode(
                e, task, page, redis, playwright_instance, initialize_browser, is_task_exception=False)
            if control == "shutdown":
                return
        finally:
            # 资源清理和任务结果发布
            global is_task_running, current_task_info
            is_task_running = False
            current_task_info = None
            if task:
                task_id = task.get("id")
                task_result_channel = task.get("result_channel")
                if task_result_channel and result:
                    await redis.publish(task_result_channel, json.dumps(result))
                    print(f"向频道 {task_result_channel} 发布了任务 {task_id} 的结果: {result}")
                lock_key = task.get("lock_key")
                if lock_key:
                    await redis.delete(lock_key)
                    print(f"释放了锁 {lock_key}")
                await update_worker_status(redis, "idle")


if __name__ == "__main__":
    try:
        # 启动自动清理服务
        start_auto_cleanup()
        logger.info("自动清理服务已启动")

        # 启动主程序
        asyncio.run(browser_manager())
    except KeyboardInterrupt:
        print("\n程序被手动中断。")
    finally:
        # 停止心跳任务
        try:
            asyncio.run(stop_heartbeat())
        except:
            pass

        # 停止自动清理服务
        stop_auto_cleanup()
        logger.info("自动清理服务已停止")
