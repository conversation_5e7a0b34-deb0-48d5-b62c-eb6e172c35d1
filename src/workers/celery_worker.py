"""
Celery Worker 主模块
重构后的Worker，整合所有功能模块
"""
import asyncio
import json

import redis.asyncio as aioredis
from playwright.async_api import async_playwright, Page

from src.conf.config import CONFIG
from src.core.exceptions import TaskException
from src.core.task_handler import task_handler_manager, handle_task
from src.monitors.browser_monitor import browser_monitor_manager
from src.monitors.disk_auto_cleanup import start_auto_cleanup, stop_auto_cleanup
from src.monitors.heartbeat_monitor import update_worker_status
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 全局变量，用于跟踪任务运行状态（保持兼容性）
is_task_running = False
current_task_info = None  # 当前执行的任务信息

PRECHECK_FLAG_KEY = "daily_jobFilterTrigger_precheck_passed"
PRECHECK_LOCK_KEY = "precheck_in_progress_lock"


async def browser_manager(redis: aioredis.Redis):
    """
    浏览器管理器主函数
    负责浏览器生命周期管理和任务处理
    """
    logger.info("启动SRA Worker...")

    playwright_instance = await async_playwright().start()
    page: Page = None

    async def initialize_browser():
        nonlocal page
        if page and page.context.browser.is_connected():
            await page.context.browser.close()

        await update_worker_status(redis, "initializing")
        print("正在初始化新浏览器...")
        try:
            # init_driver
            from src.core.browser_manager import init_driver
            page = await init_driver()
            print("浏览器及页面已成功创建。")
            return page
        except Exception as e:
            print(f"初始化浏览器失败: {e}")
            await update_worker_status(redis, "failed_to_start")
            return None

    page = await initialize_browser()
    if not page:
        print("初始浏览器创建失败，程序退出。")
        await playwright_instance.stop()
        return

    # 更新Worker状态
    await update_worker_status(redis, "idle")
    
    # 启动监控服务
    try:
        # 启动心跳监控（保持与旧版本一致的启动方式）
        from src.monitors.heartbeat_monitor import start_heartbeat
        await start_heartbeat(redis, interval=30)  # 30秒间隔更新心跳

        # 启动浏览器监控
        await browser_monitor_manager.start_monitoring(page, redis)

        # 启动自动清理
        start_auto_cleanup()

        logger.info("所有监控服务启动成功")

    except Exception as e:
        logger.error(f"启动监控服务失败: {e}")
        return

    # 主循环
    while True:
        task = None
        result = None
        try:
            # 检查任务队列
            task_key = f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.worker_ip}"
            logger.info(f"\n等待来自任务队列 '{task_key}' 的新任务...")
            _, raw = await redis.blpop(task_key)
            task = json.loads(raw)
            logger.info(f"task_data: {task}")
            if task:
                task_id = task.get("id", "unknown")

                # 仅对 jobFilterTrigger 任务执行预检查
                if task.get("action") == "jobFilterTrigger":
                    precheck_passed = await redis.get(PRECHECK_FLAG_KEY)
                    if not precheck_passed:
                        # 尝试获取预检锁
                        if await redis.set(PRECHECK_LOCK_KEY, "locked", ex=3000, nx=True):
                            logger.info(f"🔑 成功获取预检锁，本任务 {task_id} 将作为预检任务执行。")
                            # 获取锁成功，继续执行任务
                        else:
                            # 获取锁失败，将任务重新放回队列并等待
                            logger.info("预检任务正在由其他 Worker 执行，将任务重新放回队列...")
                            await redis.lpush(task_key, json.dumps(task))
                            await asyncio.sleep(10)
                            continue # 重新进入循环，等待预检完成

                logger.info(f"收到任务: {task_id}")

                # 更新任务状态为pending
                await update_worker_status(redis, "pending", task_info=task)

                # 处理任务（不删除队列，任务已通过blpop取出）
                result = await handle_task(page, task, redis)

                # 如果是预检任务且成功完成，设置预检查标志
                if task.get("action") == "jobFilterTrigger" and result and result.get("status") != "error":
                    try:
                        precheck_passed = await redis.get(PRECHECK_FLAG_KEY)
                        if not precheck_passed:
                            # 使用 pipeline 确保原子性
                            pipe = redis.pipeline()
                            pipe.set(PRECHECK_FLAG_KEY, "1", ex=86400)
                            pipe.delete(PRECHECK_LOCK_KEY)
                            await pipe.execute()
                            logger.info(f"🔑 预检任务成功，已设置完成标志并释放锁。")
                    except Exception as e:
                        logger.error(f"❌ 设置预检查标志时出错: {e}")

                # 更新任务状态为finished
                await update_worker_status(redis, "finished", task_info=task)

                logger.info(f"任务处理完成: {task_id}, result: {result}")

                await asyncio.sleep(1)  # 每秒检查一次

        except TaskException as e:
            result, control, page = await task_handler_manager.handle_exception_and_enter_management_mode(
                e, task, page, redis, playwright_instance, initialize_browser, is_task_exception=True)
            if control == "shutdown":
                return
        except Exception as e:
            result, control, page = await task_handler_manager.handle_exception_and_enter_management_mode(
                e, task, page, redis, playwright_instance, initialize_browser, is_task_exception=False)
            if control == "shutdown":
                return
        finally:
            # 资源清理和任务结果发布
            global is_task_running, current_task_info
            is_task_running = False
            current_task_info = None
            if task:
                task_id = task.get("id")
                task_result_channel = task.get("result_channel")
                if task_result_channel and result:
                    await redis.publish(task_result_channel, json.dumps(result))
                    print(f"向频道 {task_result_channel} 发布了任务 {task_id} 的结果: {result}")
                lock_key = task.get("lock_key")
                if lock_key:
                    await redis.delete(lock_key)
                    print(f"释放了锁 {lock_key}")
                await update_worker_status(redis, "idle")



async def cleanup_resources(redis: aioredis.Redis):
    """清理资源"""
    logger.info("开始清理资源...")
    
    try:
        # 停止监控服务
        await browser_monitor_manager.stop_monitoring()
        from src.monitors.heartbeat_monitor import stop_heartbeat
        await stop_heartbeat()
        stop_auto_cleanup()
        
        # 更新Worker状态
        await update_worker_status(redis, "shutdown")
        
        # 关闭Redis连接
        await redis.close()
        
        logger.info("资源清理完成")
        
    except Exception as e:
        logger.error(f"资源清理异常: {e}")


if __name__ == "__main__":
    try:
        # 启动自动清理服务（与旧版本保持一致）
        start_auto_cleanup()
        logger.info("自动清理服务已启动")

        # 启动主程序
        redis = aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
        asyncio.run(browser_manager(redis))
    except KeyboardInterrupt:
        print("\n程序被手动中断。")
    finally:
        # 停止心跳任务（与旧版本保持一致）
        try:
            from src.monitors.heartbeat_monitor import stop_heartbeat
            asyncio.run(stop_heartbeat())
        except:
            pass

        # 停止自动清理服务
        stop_auto_cleanup()
        logger.info("自动清理服务已停止")
