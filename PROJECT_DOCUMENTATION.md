# SRA (Smart Recruitment Assistant) - 项目技术文档

## 1. 项目概述

SRA (Smart Recruitment Assistant) 是一个基于 Python、FastAPI 和 Playwright 构建的、高度自动化的分布式浏览器操作平台。其核心设计目标是驱动无头浏览器集群，模拟真实用户行为，执行复杂的 Web 操作流程，尤其专注于招聘场景下的候选人筛选和信息提取。系统通过 RESTful API 接收指令，利用 Redis 作为任务队列和状态存储，实现了任务的异步处理、分布式执行和健壮的监控与错误恢复机制。

## 2. 核心架构

系统采用典型的生产者-消费者模型，解耦了任务的派发与执行。

- **生产者 (Producer)**: `FastAPI` 应用作为任务的生产者。它接收来自外部系统的 HTTP 请求，将其封装成标准化的任务（JSON 格式），并推送到 Redis 任务队列中。
- **消息代理 (Broker)**: `Redis` 在此架构中扮演核心角色，不仅作为任务队列，还用于分布式锁、状态同步、结果缓存和发布/订阅消息通知。
- **消费者 (Consumer)**: `Celery Worker` (`src/workers/celery_worker.py`) 是任务的消费者。它持续监听 Redis 队列，获取并执行任务，驱动 Playwright 浏览器完成指定的 Web 操作。

这种架构带来了以下优势：
- **高可伸缩性**: 可以通过简单地增加 Worker 节点的数量来水平扩展系统的处理能力。
- **高可用性**: 单个 Worker 节点的故障不会影响整个系统的运行。
- **异步处理**: API 调用可以快速返回，无需等待耗时的浏览器操作完成，提升了用户体验。

## 3. 模块详解

### `src/conf` - 配置模块

- **`config.py`**: 项目的唯一配置中心。`Config` 类集中管理了所有硬编码的参数，如 Redis 连接信息、第三方服务的 URL、API 端点、文件路径、爬虫行为延迟、反检测策略以及日志和监控的各项参数。这种设计使得配置的修改和维护变得非常方便。

### `src/core` - 核心功能模块

- **`browser_manager.py`**: 负责 Playwright 浏览器的初始化和管理。`init_driver` 函数是关键入口，它不仅启动浏览器，还注入了用于反检测的 JavaScript Hook 脚本，并设置了请求路由拦截，以模拟更真实的用户行为。
- **`task_handler.py`**: 任务处理的调度中心。`TaskHandlerManager` 负责从 Worker 主循环接收任务，并调用 `task_processor` 来获取具体的处理器。它还包含了异常处理和进入“管理模式”的核心逻辑。
- **`task_processor.py`**: 任务处理器的工厂和基类。`BaseTaskProcessor` 定义了所有任务处理器的标准接口（`validate_params`, `execute_task`, `process`）。`get_task_processor` 函数则根据任务的 `action` 字段，返回一个具体的处理器实例（如 `JobFilterProcessor`）。
- **`exceptions.py`**: 定义了项目中所有的自定义异常类（如 `TaskException`, `BrowserException`），并提供了统一的错误代码（`ErrorCodes`），使得错误处理更加规范和清晰。

### `src/flows` - 业务流程模块

此模块包含了实现具体业务逻辑的脚本，是系统功能的核心。

- **`login.py`**: 实现了完整的登录流程，包括通过本地 Cookie 自动登录和通过二维码扫码登录。`BrowserSessionHandler` 类负责管理和持久化用户的登录状态。
- **`geek_fetch_flow.py`**: 这是最核心的业务流程，负责“牛人”的筛选和信息抓取。`fetch_recommended_geeks` 函数编排了页面导航、职位选择、列表滚动、API 数据监听、详情页处理等一系列复杂操作。
- **`geek_filter.py`**: 调用后端的筛选接口，根据候选人信息和职位详情，判断是否需要进一步操作（如收藏、打招呼）。
- **`geek_info_build.py`**: 负责从简历的 Canvas 和 DOM 结构中提取结构化信息。`HybridStructureExtractor` 类是其核心，它融合了从 API、DOM 和 Canvas 文本中提取的数据，生成最终的候选人简历。
- **`callback.py`**: 封装了所有与外部系统进行通信的回调函数，如任务成功/失败回调、登录状态同步等。

### `src/monitors` - 监控模块

- **`heartbeat_monitor.py`**: 实现 Worker 的心跳机制。定期向 Redis 写入状态，表明 Worker 存活，并允许外部系统监控 Worker 的健康状况。
- **`browser_monitor.py`**: 监控浏览器进程的健康状况。通过定期检查 `page.is_closed()` 和 `browser.is_connected()`，能够及时发现浏览器是否意外崩溃或关闭，并发送告警。
- **`disk_auto_cleanup.py`**: 一个独立的后台服务，使用 `schedule` 库定期检查磁盘使用率和 Playwright 临时文件的大小，并在达到阈值时自动执行清理任务，防止磁盘空间被耗尽。

### `src/routers` - API 路由模块

此模块使用 FastAPI 定义了所有对外暴露的 HTTP 接口。

- **`agent_api.py`**: 提供了代理操作相关的主要接口，如 `/login` 和 `/jobfilter/trigger`，这些是驱动 Worker 执行核心任务的入口。
- **`dispatch_api.py`**: 提供了更通用的任务派发接口，支持同步 (`/sync`) 和异步 (`/async`) 两种模式。
- **`ctrl_api.py`**: 提供了对 Worker 进行管理的控制接口，如获取状态 (`/status`) 和发送命令 (`/command`)。
- **`api_result.py`**: 实现了一个精巧的 `ResultManager`，它通过 Redis 的发布/订阅模式，解决了在同步的 API 请求中等待异步 Worker 执行结果的问题。

### `src/utils` - 工具模块

- **`logger.py`**: 基于 `loguru` 实现了强大且配置灵活的日志系统。支持多级别、多目标（控制台和文件）输出，并能自动进行日志轮转和压缩。
- **`mailer.py`**: 封装了企业微信机器人的消息发送功能，用于发送格式化的监控告警和任务通知。
- **`tracing_manager.py`**: 管理 Playwright 的 Tracing 功能。它可以在操作的关键节点自动开始和停止记录，生成可用于回溯和调试的 `.zip` 文件，并内置了磁盘空间管理策略，防止 trace 文件无限增长。

### `src/workers` - Worker 模块

- **`celery_worker.py`**: Worker 进程的启动入口和主控制循环。`browser_manager` 函数是其核心，负责初始化所有组件（浏览器、Redis 连接、监控服务），并进入一个无限循环来处理任务。该文件还实现了健壮的预检查和分布式锁机制。

## 4. 关键机制详解

### 预检查与分布式锁

为了确保系统的稳定运行，特别是避免在外部服务（如 Boss 直聘）发生变更时造成大规模任务失败，系统实现了一套动态的、基于分布式锁的预检查机制。

1.  **触发**: 当天的第一个 `jobFilterTrigger` 任务被视为“预检任务”。
2.  **锁定**: 为了防止多个 Worker 同时执行预检，系统引入了一个全局的“预检锁” (`precheck_in_progress_lock`)。当一个 Worker 准备执行预检时，它必须先以原子方式（`SETNX`）获取这个锁。
3.  **等待**: 未能获取锁的 Worker 会进入等待状态，定期检查“预检完成”标志。
4.  **执行与标记**: 成功获取锁的 Worker 会完整地执行 `geek_fetch_flow.py` 流程。当流程成功执行到关键步骤（例如，成功获取到第一页候选人列表），它会在 Redis 中设置一个“预检完成”标志 (`daily_jobFilterTrigger_precheck_passed`)，并为其设置 24 小时过期。
5.  **释放与唤醒**: 在设置完成标志后，该 Worker 会立即删除它持有的“预检锁”。锁的释放和标志的设置会“唤醒”其他正在等待的 Worker，使它们能够开始正常处理各自的任务。

这个机制确保了只有在核心流程被验证为可用后，系统才会开始大规模处理任务，极大地提高了整体的健壮性。

### 异常处理与管理模式

当任务执行过程中发生严重异常时，`TaskHandlerManager` 会捕获异常，并通过 `mailer` 发送详细的告警信息。随后，它会尝试进入“管理模式”，等待人工干预。管理员可以通过调用 `/worker/command` API 发送指令（如 `resume`, `restart_browser`, `shutdown`）来远程控制 Worker 的行为。

## 5. 部署与运维

### 依赖安装

```bash
# 创建并激活虚拟环境
python -m venv .venv
.venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 服务启停 (Windows)

使用项目根目录下的 `restart_services.bat` 脚本可以一键启动或重启所有服务。该脚本会自动处理进程的停止、虚拟环境的激活和后台服务的启动。

### 开机自启动 (Windows)

为了实现无人值守运行，可以执行 `register_autostart.ps1` 脚本（使用 PowerShell 运行）。该脚本会自动创建一个 Windows 任务计划，在系统启动时执行 `restart_services.bat`，从而实现服务的开机自启动。