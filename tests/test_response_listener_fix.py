#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试响应监听器修复效果的脚本
"""

import asyncio
import logging
from unittest.mock import Mock, AsyncMock

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class MockPage:
    """模拟Playwright Page对象"""
    
    def __init__(self):
        self.listeners = []
        self.response_count = 0
    
    def on(self, event, handler):
        """添加事件监听器"""
        if event == "response":
            self.listeners.append(handler)
            logger.info(f"添加监听器，当前监听器数量: {len(self.listeners)}")
    
    def remove_listener(self, event, handler):
        """移除事件监听器"""
        if event == "response" and handler in self.listeners:
            self.listeners.remove(handler)
            logger.info(f"移除监听器，当前监听器数量: {len(self.listeners)}")
        else:
            raise ValueError("监听器不存在")
    
    async def trigger_response(self, url="https://www.zhipin.com/wapi/zpgeek/search/joblist.json"):
        """模拟触发响应事件"""
        self.response_count += 1
        mock_response = Mock()
        mock_response.url = url
        mock_response.request.method = 'GET'
        mock_response.json = AsyncMock(return_value={
            "code": 0,
            "zpData": {
                "geekList": [{"encryptGeekId": f"test_{self.response_count}"}],
                "hasMore": False
            }
        })
        
        logger.info(f"触发响应事件 #{self.response_count}，将调用 {len(self.listeners)} 个监听器")
        
        # 调用所有监听器
        for i, listener in enumerate(self.listeners):
            logger.info(f"调用监听器 #{i+1}")
            await listener(mock_response)

class ResponseListenerManager:
    """响应监听器管理器，确保监听器的正确添加和清理"""
    
    def __init__(self, page):
        self.page = page
        self.handler = None
        self.is_active = False
    
    def add_listener(self, handler):
        """添加响应监听器"""
        if self.is_active and self.handler:
            # 如果已有监听器，先清理
            self.remove_listener()
        
        self.handler = handler
        self.page.on("response", handler)
        self.is_active = True
        logger.debug("已添加响应监听器")
    
    def remove_listener(self):
        """移除响应监听器"""
        if self.is_active and self.handler:
            try:
                self.page.remove_listener("response", self.handler)
                logger.debug("已移除响应监听器")
            except Exception as e:
                logger.debug(f"移除监听器时出错: {e}")
            finally:
                self.handler = None
                self.is_active = False

async def test_listener_management():
    """测试监听器管理"""
    logger.info("=== 开始测试监听器管理 ===")
    
    page = MockPage()
    manager = ResponseListenerManager(page)
    
    # 创建测试处理器
    async def handler1(response):
        logger.info("Handler1: 处理响应")
    
    async def handler2(response):
        logger.info("Handler2: 处理响应")
    
    # 测试1: 添加第一个监听器
    logger.info("\n--- 测试1: 添加第一个监听器 ---")
    manager.add_listener(handler1)
    await page.trigger_response()
    
    # 测试2: 添加第二个监听器（应该替换第一个）
    logger.info("\n--- 测试2: 添加第二个监听器 ---")
    manager.add_listener(handler2)
    await page.trigger_response()
    
    # 测试3: 手动移除监听器
    logger.info("\n--- 测试3: 手动移除监听器 ---")
    manager.remove_listener()
    await page.trigger_response()
    
    logger.info(f"\n最终监听器数量: {len(page.listeners)}")
    logger.info("=== 测试完成 ===")

async def simulate_recursive_calls():
    """模拟递归调用场景"""
    logger.info("\n=== 模拟递归调用场景 ===")
    
    page = MockPage()
    call_count = 0
    max_calls = 3
    
    async def mock_fetch_function():
        nonlocal call_count
        call_count += 1
        logger.info(f"\n--- 第 {call_count} 次调用 ---")
        
        manager = ResponseListenerManager(page)
        
        async def handle_response(response):
            logger.info(f"处理响应 - 调用 #{call_count}")
        
        # 添加监听器
        manager.add_listener(handle_response)
        
        # 模拟触发响应
        await page.trigger_response()
        
        # 模拟递归调用
        if call_count < max_calls:
            # 清理监听器
            manager.remove_listener()
            # 递归调用
            await mock_fetch_function()
        else:
            # 最后一次调用，清理监听器
            manager.remove_listener()
    
    await mock_fetch_function()
    
    logger.info(f"\n递归调用完成，最终监听器数量: {len(page.listeners)}")
    logger.info("=== 递归调用测试完成 ===")

async def main():
    """主测试函数"""
    await test_listener_management()
    await simulate_recursive_calls()

if __name__ == "__main__":
    asyncio.run(main())
