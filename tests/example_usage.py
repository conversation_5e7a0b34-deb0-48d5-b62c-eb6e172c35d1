#!/usr/bin/env python3
"""
Boss直聘简历详情获取功能使用示例
"""
import asyncio
import json
import uuid
import time
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import redis.asyncio as aioredis
from src.conf.config import CONFIG


class ResumeDetailClient:
    """简历详情获取客户端"""
    
    def __init__(self):
        self.redis = None
        self.queue_name = f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.worker_ip}"
    
    async def connect(self):
        """连接Redis"""
        self.redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis:
            await self.redis.close()
    
    async def get_resume_detail(self, resume_url: str, timeout: int = 60) -> dict:
        """
        获取简历详情
        
        Args:
            resume_url: 简历分享页面URL
            timeout: 超时时间（秒）
            
        Returns:
            dict: 包含简历详情的字典
        """
        if not self.redis:
            await self.connect()
        
        # 生成任务ID和结果频道
        task_id = f"resume_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        result_channel = f"result_{task_id}"
        
        # 构建任务
        task = {
            "id": task_id,
            "action": "get_resume_detail",
            "payload": {
                "url": resume_url
            },
            "result_channel": result_channel,
            "timestamp": time.time()
        }
        
        print(f"发送任务: {task_id}")
        print(f"URL: {resume_url}")
        
        try:
            # 发送任务到队列
            await self.redis.rpush(self.queue_name, json.dumps(task))
            
            # 订阅结果频道
            pubsub = self.redis.pubsub()
            await pubsub.subscribe(result_channel)
            
            # 等待结果
            start_time = time.time()
            
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        result = json.loads(message['data'])
                        return result
                    except json.JSONDecodeError as e:
                        return {
                            "status": "error",
                            "id": task_id,
                            "message": f"解析结果失败: {e}"
                        }
                
                # 检查超时
                if time.time() - start_time > timeout:
                    return {
                        "status": "error",
                        "id": task_id,
                        "message": "等待结果超时"
                    }
            
        except Exception as e:
            return {
                "status": "error",
                "id": task_id,
                "message": f"发送任务失败: {e}"
            }
        
        finally:
            try:
                await pubsub.unsubscribe(result_channel)
            except:
                pass
    
    async def batch_get_resume_details(self, resume_urls: list, max_concurrent: int = 1) -> list:
        """
        批量获取简历详情（由于并发限制，实际是串行处理）
        
        Args:
            resume_urls: 简历URL列表
            max_concurrent: 最大并发数（当前固定为1）
            
        Returns:
            list: 结果列表
        """
        results = []
        
        for i, url in enumerate(resume_urls):
            print(f"\n处理第 {i+1}/{len(resume_urls)} 个简历...")
            result = await self.get_resume_detail(url)
            results.append(result)
            
            # 如果不是最后一个，等待一段时间避免过于频繁
            if i < len(resume_urls) - 1:
                await asyncio.sleep(2)
        
        return results


async def example_single_resume():
    """示例：获取单个简历详情"""
    print("=== 单个简历详情获取示例 ===")
    
    client = ResumeDetailClient()
    
    try:
        # 示例URL（需要替换为真实的简历分享URL）
        resume_url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        
        result = await client.get_resume_detail(resume_url)
        
        print(f"\n任务结果:")
        print(f"状态: {result.get('status')}")
        print(f"任务ID: {result.get('id')}")
        
        if result.get('status') == 'success':
            data = result.get('data', {})
            print(f"\n简历信息:")
            print(f"姓名: {data.get('geekName', '未知')}")
            print(f"年龄: {data.get('ageDesc', '未知')}")
            print(f"工作年限: {data.get('workYearDesc', '未知')}")
            print(f"期望薪资: {data.get('salaryDesc', '未知')}")
            print(f"期望职位: {data.get('workEduDesc', '未知')}")
            print(f"活跃时间: {data.get('activeTimeDesc', '未知')}")
            
            # 显示简历描述（截取前200字符）
            geek_desc = data.get('geekDesc', '')
            if geek_desc:
                print(f"简历描述: {geek_desc[:200]}{'...' if len(geek_desc) > 200 else ''}")
        
        elif result.get('status') == 'error':
            print(f"错误: {result.get('message')}")
    
    except Exception as e:
        print(f"示例执行失败: {e}")
    
    finally:
        await client.disconnect()


async def example_batch_resumes():
    """示例：批量获取简历详情"""
    print("=== 批量简历详情获取示例 ===")
    
    client = ResumeDetailClient()
    
    try:
        # 示例URL列表（需要替换为真实的简历分享URL）
        resume_urls = [
            "https://www.zhipin.com/web/boss/resume/share?shareId=35910040",
            "https://www.zhipin.com/web/boss/resume/share?shareId=35910041",
            "https://www.zhipin.com/web/boss/resume/share?shareId=35910042"
        ]
        
        print(f"准备获取 {len(resume_urls)} 个简历的详情...")
        
        results = await client.batch_get_resume_details(resume_urls)
        
        print(f"\n批量处理完成，共处理 {len(results)} 个简历:")
        
        success_count = 0
        error_count = 0
        
        for i, result in enumerate(results):
            print(f"\n简历 {i+1}:")
            print(f"  状态: {result.get('status')}")
            
            if result.get('status') == 'success':
                success_count += 1
                data = result.get('data', {})
                print(f"  姓名: {data.get('geekName', '未知')}")
                print(f"  期望薪资: {data.get('salaryDesc', '未知')}")
            else:
                error_count += 1
                print(f"  错误: {result.get('message')}")
        
        print(f"\n处理统计:")
        print(f"  成功: {success_count}")
        print(f"  失败: {error_count}")
        print(f"  总计: {len(results)}")
    
    except Exception as e:
        print(f"批量示例执行失败: {e}")
    
    finally:
        await client.disconnect()


async def example_error_handling():
    """示例：错误处理"""
    print("=== 错误处理示例 ===")
    
    client = ResumeDetailClient()
    
    try:
        # 测试各种错误情况
        test_cases = [
            {
                "name": "无效URL",
                "url": "https://invalid-url.com"
            },
            {
                "name": "缺少参数",
                "url": ""
            },
            {
                "name": "正常URL",
                "url": "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
            }
        ]
        
        for test_case in test_cases:
            print(f"\n测试: {test_case['name']}")
            print(f"URL: {test_case['url']}")
            
            if not test_case['url']:
                print("跳过空URL测试")
                continue
            
            result = await client.get_resume_detail(test_case['url'], timeout=30)
            
            print(f"结果: {result.get('status')}")
            if result.get('status') == 'error':
                print(f"错误信息: {result.get('message')}")
            elif result.get('status') == 'success':
                data = result.get('data', {})
                print(f"成功获取: {data.get('geekName', '未知')}")
            
            # 等待一段时间再进行下一个测试
            await asyncio.sleep(3)
    
    except Exception as e:
        print(f"错误处理示例失败: {e}")
    
    finally:
        await client.disconnect()


def main():
    """主函数"""
    print("Boss直聘简历详情获取功能示例")
    print("=" * 50)
    
    print("选择示例:")
    print("1. 单个简历详情获取")
    print("2. 批量简历详情获取")
    print("3. 错误处理示例")
    print("4. 运行所有示例")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        asyncio.run(example_single_resume())
    elif choice == "2":
        asyncio.run(example_batch_resumes())
    elif choice == "3":
        asyncio.run(example_error_handling())
    elif choice == "4":
        print("运行所有示例...")
        asyncio.run(example_single_resume())
        print("\n" + "="*50 + "\n")
        asyncio.run(example_batch_resumes())
        print("\n" + "="*50 + "\n")
        asyncio.run(example_error_handling())
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
