#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的cookie文件检测测试脚本
"""

import os
import json
import glob
from datetime import datetime

def find_user_cookie_files():
    """查找用户cookie文件"""
    data_dir = "./data"
    pattern = os.path.join(data_dir, "user_*.json")
    cookie_files = glob.glob(pattern)
    
    print(f"在 {data_dir} 目录下查找模式: user_*.json")
    print(f"找到 {len(cookie_files)} 个文件")
    
    available_cookies = []
    
    for cookie_file in cookie_files:
        try:
            # 从文件名提取账号名
            filename = os.path.basename(cookie_file)
            account_name = filename.replace("user_", "").replace(".json", "")
            
            # 检查文件是否有效
            with open(cookie_file, "r", encoding="utf-8") as f:
                login_data = json.load(f)
            
            if login_data.get("cookies"):
                file_mtime = os.path.getmtime(cookie_file)
                available_cookies.append({
                    "file_path": cookie_file,
                    "account_name": account_name,
                    "modified_time": file_mtime,
                    "cookie_count": len(login_data["cookies"])
                })
                print(f"✅ 有效文件: {cookie_file}")
                print(f"   账号: {account_name}")
                print(f"   Cookie数量: {len(login_data['cookies'])}")
                print(f"   修改时间: {datetime.fromtimestamp(file_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print(f"❌ 无效文件: {cookie_file} (没有cookies数据)")
                
        except Exception as e:
            print(f"❌ 错误文件: {cookie_file} - {e}")
    
    # 按修改时间排序
    available_cookies.sort(key=lambda x: x["modified_time"], reverse=True)
    
    return available_cookies

def validate_cookie_structure(cookie_file):
    """验证cookie文件结构"""
    print(f"\n=== 验证cookie文件结构: {cookie_file} ===")
    
    try:
        with open(cookie_file, "r", encoding="utf-8") as f:
            login_data = json.load(f)
        
        cookies = login_data.get("cookies", [])
        print(f"总cookie数量: {len(cookies)}")
        
        # 验证cookie结构
        required_fields = ["name", "value", "domain"]
        valid_cookies = 0
        
        domains = {}
        
        for i, cookie in enumerate(cookies):
            if all(field in cookie for field in required_fields):
                valid_cookies += 1
                domain = cookie.get("domain", "unknown")
                domains[domain] = domains.get(domain, 0) + 1
                
                # 显示前3个cookie的详细信息
                if i < 3:
                    print(f"  Cookie {i+1}:")
                    print(f"    名称: {cookie.get('name')}")
                    print(f"    值: {cookie.get('value')[:20]}...")
                    print(f"    域名: {cookie.get('domain')}")
                    print(f"    路径: {cookie.get('path', 'N/A')}")
            else:
                print(f"  ❌ 无效cookie {i+1}: 缺少必要字段")
        
        print(f"有效cookie数量: {valid_cookies}/{len(cookies)}")
        
        print("\nCookie域名分布:")
        for domain, count in domains.items():
            print(f"  {domain}: {count} 个")
            
        return valid_cookies > 0
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def simulate_auto_load():
    """模拟自动加载过程"""
    print("\n=== 模拟自动加载过程 ===")
    
    available_cookies = find_user_cookie_files()
    
    if not available_cookies:
        print("❌ 没有找到可用的cookie文件")
        return False
    
    # 选择最新的文件
    latest_cookie = available_cookies[0]
    print(f"\n选择最新的cookie文件:")
    print(f"  文件: {latest_cookie['file_path']}")
    print(f"  账号: {latest_cookie['account_name']}")
    print(f"  Cookie数量: {latest_cookie['cookie_count']}")
    
    # 验证文件结构
    is_valid = validate_cookie_structure(latest_cookie['file_path'])
    
    if is_valid:
        print(f"\n✅ 自动加载模拟成功!")
        print(f"将设置环境变量: account_name = {latest_cookie['account_name']}")
        print(f"将加载 {latest_cookie['cookie_count']} 个cookie到浏览器")
        return True
    else:
        print(f"\n❌ Cookie文件无效，无法加载")
        return False

def main():
    """主函数"""
    print("Cookie自动加载功能测试")
    print("=" * 50)
    
    # 检查data目录
    if not os.path.exists("./data"):
        print("❌ ./data 目录不存在")
        return
    
    print(f"✅ 找到data目录: {os.path.abspath('./data')}")
    
    # 查找cookie文件
    available_cookies = find_user_cookie_files()
    
    if available_cookies:
        print(f"\n✅ 总共找到 {len(available_cookies)} 个有效的cookie文件")
        
        # 模拟自动加载
        success = simulate_auto_load()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 自动加载cookie功能测试通过!")
            print("\n使用说明:")
            print("1. 调用 init_driver() 会自动加载最新的cookie文件")
            print("2. 使用 init_driver(auto_load_cookies=False) 可以禁用自动加载")
            print("3. 系统会自动选择最新修改的cookie文件")
            print("4. 会自动设置 account_name 环境变量")
        else:
            print("❌ 自动加载cookie功能测试失败")
    else:
        print("\n❌ 没有找到任何有效的cookie文件")
        print("\n请确保:")
        print("1. 在 ./data/ 目录下有 user_*.json 格式的文件")
        print("2. 文件包含有效的 cookies 数据")
        print("3. 文件格式正确（JSON格式）")

if __name__ == "__main__":
    main()
