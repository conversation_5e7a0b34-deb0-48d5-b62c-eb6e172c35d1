#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器异常关闭检测功能演示脚本

演示内容：
1. 启动浏览器监控
2. 模拟浏览器异常关闭
3. 展示告警功能
4. 展示管理模式
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.conf.config import CONFIG
from src.utils.logger import get_logger
from src.celery_worker import start_browser_monitor, stop_browser_monitor, detect_browser_abnormal_closure
from src.core.browser_manager import init_driver

logger = get_logger(__name__)


async def demo_browser_monitor():
    """演示浏览器监控功能"""
    print("🚀 开始浏览器异常关闭检测功能演示...")
    print("=" * 60)
    
    # 初始化浏览器
    print("1. 初始化浏览器...")
    page = await init_driver()
    print("✅ 浏览器初始化成功")
    
    # 连接Redis
    import redis.asyncio as aioredis
    redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
    print("✅ Redis连接成功")
    
    try:
        # 启动浏览器监控
        print("\n2. 启动浏览器监控任务...")
        await start_browser_monitor(page, redis, interval=10)
        print("✅ 浏览器监控任务已启动（检测间隔：10秒）")
        
        # 等待一段时间让监控运行
        print("\n3. 监控任务运行中...")
        for i in range(3):
            print(f"   监控运行中... ({i+1}/3)")
            await asyncio.sleep(5)
        
        # 测试正常状态检测
        print("\n4. 测试正常状态检测...")
        is_abnormal = await detect_browser_abnormal_closure(page, "demo_account")
        if not is_abnormal:
            print("✅ 正常状态检测正确（未检测到异常关闭）")
        else:
            print("❌ 误报异常关闭")
        
        # 模拟浏览器异常关闭
        print("\n5. 模拟浏览器异常关闭...")
        print("   正在关闭浏览器...")
        await page.context.browser.close()
        
        # 等待检测
        print("   等待检测异常关闭...")
        await asyncio.sleep(3)
        
        # 测试异常状态检测
        print("\n6. 测试异常状态检测...")
        is_abnormal = await detect_browser_abnormal_closure(page, "demo_account")
        if is_abnormal:
            print("✅ 成功检测到浏览器异常关闭")
        else:
            print("❌ 未能检测到浏览器异常关闭")
        
        # 停止监控任务
        print("\n7. 停止浏览器监控任务...")
        await stop_browser_monitor()
        print("✅ 浏览器监控任务已停止")
        
    except Exception as e:
        print(f"❌ 演示过程中出现异常: {e}")
        
    finally:
        # 清理资源
        try:
            if page and not page.is_closed():
                await page.context.browser.close()
            await redis.close()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️  资源清理时出现异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 浏览器异常关闭检测功能演示完成！")
    print("\n功能特点:")
    print("✅ 实时监控浏览器状态")
    print("✅ 自动检测异常关闭")
    print("✅ 企业微信告警通知")
    print("✅ 进入管理模式等待处理")
    print("✅ 支持浏览器重启恢复")


if __name__ == "__main__":
    asyncio.run(demo_browser_monitor())
