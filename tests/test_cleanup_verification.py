#!/usr/bin/env python3
"""
代码清理后的验证测试脚本
确保删除无用代码后，主要功能仍然正常工作
"""
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class CleanupVerificationTester:
    """代码清理验证测试器"""
    
    def __init__(self):
        self.test_results = []
    
    def record_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}: {message}")
    
    def test_main_imports(self):
        """测试主要模块的导入"""
        test_name = "主要模块导入测试"
        
        try:
            # 测试主程序入口
            import src.fast_api
            self.record_test_result(f"{test_name} - fast_api", True, "FastAPI模块导入成功")
            
            import src.celery_worker
            self.record_test_result(f"{test_name} - celery_worker", True, "Worker模块导入成功")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"主程序导入失败: {e}")
    
    def test_core_modules(self):
        """测试核心模块"""
        test_name = "核心模块测试"
        
        try:
            # 测试核心模块
            from src.core.browser_manager import init_driver
            self.record_test_result(f"{test_name} - browser_manager", True, "浏览器管理器正常")
            
            from src.core.compatibility import handle_task_legacy
            self.record_test_result(f"{test_name} - compatibility", True, "兼容性适配器正常")
            
            from src.core.task_processor import get_task_processor
            self.record_test_result(f"{test_name} - task_processor", True, "任务处理器正常")
            
            from src.core.exceptions import SRABaseException
            self.record_test_result(f"{test_name} - exceptions", True, "异常定义正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"核心模块导入失败: {e}")
    
    def test_api_modules(self):
        """测试API模块"""
        test_name = "API模块测试"
        
        try:
            # 测试API路由
            from src.routers.agent_api import router as agent_router
            self.record_test_result(f"{test_name} - agent_api", True, "代理API正常")
            
            from src.routers.ctrl_api import router as ctrl_router
            self.record_test_result(f"{test_name} - ctrl_api", True, "控制API正常")
            
            from src.routers.dispatch_api import router as dispatch_router
            self.record_test_result(f"{test_name} - dispatch_api", True, "分发API正常")
            
            from src.routers.api_result import router as result_router
            self.record_test_result(f"{test_name} - api_result", True, "结果API正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"API模块导入失败: {e}")
    
    def test_flow_modules(self):
        """测试业务流程模块"""
        test_name = "业务流程模块测试"
        
        try:
            # 测试业务流程
            from src.flows.login import login
            self.record_test_result(f"{test_name} - login", True, "登录流程正常")
            
            from src.flows.geek_fetch_flow import fetch_recommended_geeks
            self.record_test_result(f"{test_name} - geek_fetch_flow", True, "牛人抓取流程正常")
            
            from src.flows.geek_info_build import build_geek_info
            self.record_test_result(f"{test_name} - geek_info_build", True, "信息构建流程正常")
            
            from src.flows.geek_filter import filter_geeks
            self.record_test_result(f"{test_name} - geek_filter", True, "牛人过滤流程正常")
            
            from src.flows.callback import error_callback
            self.record_test_result(f"{test_name} - callback", True, "回调处理正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"业务流程模块导入失败: {e}")
    
    def test_utils_modules(self):
        """测试工具模块"""
        test_name = "工具模块测试"
        
        try:
            # 测试工具模块
            from src.utils.logger import get_logger
            self.record_test_result(f"{test_name} - logger", True, "日志工具正常")
            
            from src.utils.mailer import get_wechat_bot
            self.record_test_result(f"{test_name} - mailer", True, "企业微信机器人正常")
            
            from src.utils.retry_handler import retry_on_failure
            self.record_test_result(f"{test_name} - retry_handler", True, "重试处理正常")
            
            from src.utils.tracing_manager import start_trace
            self.record_test_result(f"{test_name} - tracing_manager", True, "追踪管理正常")
            
            from src.utils.task_recovery import create_task
            self.record_test_result(f"{test_name} - task_recovery", True, "任务恢复正常")
            
            from src.utils.log_archiver import start_log_archiver
            self.record_test_result(f"{test_name} - log_archiver", True, "日志归档正常")
            
            from src.monitors.disk_auto_cleanup import start_auto_cleanup
            self.record_test_result(f"{test_name} - auto_cleanup", True, "自动清理正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"工具模块导入失败: {e}")
    
    def test_deleted_files_not_imported(self):
        """测试已删除的文件确实不再被导入"""
        test_name = "已删除文件验证"
        
        deleted_modules = [
            "src.core.browser_config",
            "src.services.browser_service", 
            "src.core.worker_manager",
            "src.main_worker",
            "src.utils.anti_detection",
            "src.utils.create_baseline",
            "src.flows.validation_flow",
            "src.flows.job_agent"
        ]
        
        for module_name in deleted_modules:
            try:
                __import__(module_name)
                self.record_test_result(f"{test_name} - {module_name}", False, "文件仍然存在，删除失败")
            except ImportError:
                self.record_test_result(f"{test_name} - {module_name}", True, "文件已正确删除")
            except Exception as e:
                self.record_test_result(f"{test_name} - {module_name}", False, f"测试异常: {e}")
    
    def test_configuration(self):
        """测试配置模块"""
        test_name = "配置模块测试"
        
        try:
            from src.conf.config import CONFIG
            self.record_test_result(f"{test_name} - config", True, "配置模块正常")
            
            # 测试配置项是否可访问
            user_id = CONFIG.worker_ip
            redis_url = CONFIG.Redis.URL
            self.record_test_result(f"{test_name} - config_access", True, "配置项访问正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"配置模块测试失败: {e}")
    
    def test_management_tools(self):
        """测试管理工具"""
        test_name = "管理工具测试"
        
        try:
            from src.management.task_manager import TaskManager
            self.record_test_result(f"{test_name} - task_manager", True, "任务管理器正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"管理工具测试失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("代码清理后验证测试")
        print("=" * 50)
        
        # 运行各项测试
        self.test_main_imports()
        self.test_core_modules()
        self.test_api_modules()
        self.test_flow_modules()
        self.test_utils_modules()
        self.test_configuration()
        self.test_management_tools()
        self.test_deleted_files_not_imported()
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n测试完成统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        # 显示失败的测试
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        return failed_tests == 0
    
    def print_detailed_results(self):
        """打印详细测试结果"""
        print("\n" + "="*60)
        print("代码清理验证详细结果")
        print("="*60)
        
        for result in self.test_results:
            status = "PASS" if result["success"] else "FAIL"
            print(f"[{status}] {result['test_name']}")
            if result["message"]:
                print(f"      {result['message']}")
        
        print("="*60)


def main():
    """主函数"""
    print("SRA项目代码清理验证测试")
    print("检查删除无用代码后，主要功能是否正常")
    print("=" * 50)
    
    tester = CleanupVerificationTester()
    
    try:
        success = tester.run_all_tests()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        if success:
            print("\n🎉 所有测试通过！代码清理成功。")
            print("✅ 主要功能模块正常")
            print("✅ 无用文件已正确删除")
            print("✅ 导入依赖关系正常")
            print("✅ 系统可以正常运行")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查代码清理结果。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
