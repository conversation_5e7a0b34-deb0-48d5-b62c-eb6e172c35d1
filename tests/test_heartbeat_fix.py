#!/usr/bin/env python3
"""
心跳修复功能测试脚本
验证celery_worker.py中的心跳更新机制是否正常工作
"""
import asyncio
import json
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import redis.asyncio as aioredis
from src.conf.config import CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)


class HeartbeatTester:
    """心跳功能测试器"""
    
    def __init__(self):
        self.test_results = []
        self.redis = None
    
    async def setup(self):
        """设置测试环境"""
        try:
            self.redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
            logger.info("Redis连接已建立")
            return True
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        if self.redis:
            await self.redis.close()
    
    def record_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}: {message}")
    
    async def test_heartbeat_functions(self):
        """测试心跳函数的导入和基本功能"""
        test_name = "心跳函数导入测试"
        
        try:
            # 测试导入心跳函数
            from src.celery_worker import start_heartbeat, stop_heartbeat, update_worker_status
            
            self.record_test_result(test_name, True, "心跳函数导入成功")
            
            # 测试心跳函数的基本调用
            test_name = "心跳函数调用测试"
            
            # 启动心跳（短间隔用于测试）
            await start_heartbeat(self.redis, interval=2)
            await asyncio.sleep(1)  # 等待心跳任务启动
            
            # 停止心跳
            await stop_heartbeat()
            
            self.record_test_result(test_name, True, "心跳函数调用正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_worker_status_update(self):
        """测试worker状态更新"""
        test_name = "Worker状态更新测试"
        
        try:
            from src.celery_worker import update_worker_status
            
            # 测试状态更新
            await update_worker_status(self.redis, "idle")
            
            # 检查Redis中的状态
            status_key = f"{CONFIG.Redis.STATUS_KEY_PREFIX}{CONFIG.worker_ip}"
            status_data = await self.redis.get(status_key)
            
            if status_data:
                status = json.loads(status_data)
                if status.get("status") == "idle" and "last_update_utc" in status:
                    self.record_test_result(test_name, True, "状态更新正常，包含last_update_utc")
                else:
                    self.record_test_result(test_name, False, f"状态数据异常: {status}")
            else:
                self.record_test_result(test_name, False, "Redis中未找到状态数据")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_heartbeat_during_task(self):
        """测试任务执行期间的心跳更新"""
        test_name = "任务期间心跳测试"
        
        try:
            from src.celery_worker import start_heartbeat, stop_heartbeat, current_task_info
            
            # 模拟任务信息
            mock_task = {
                "id": "test_task_123",
                "action": "test_action",
                "timestamp": time.time()
            }
            
            # 设置当前任务信息（模拟任务开始）
            import src.celery_worker as worker_module
            worker_module.current_task_info = mock_task
            worker_module.is_task_running = True
            
            # 启动心跳
            await start_heartbeat(self.redis, interval=1)
            
            # 等待几次心跳
            await asyncio.sleep(3)
            
            # 检查状态更新
            status_key = f"{CONFIG.Redis.STATUS_KEY_PREFIX}{CONFIG.worker_ip}"
            status_data = await self.redis.get(status_key)
            
            if status_data:
                status = json.loads(status_data)
                if status.get("status") == "processing_task":
                    self.record_test_result(test_name, True, "任务期间心跳更新正常")
                else:
                    self.record_test_result(test_name, False, f"状态不正确: {status.get('status')}")
            else:
                self.record_test_result(test_name, False, "未找到状态数据")
            
            # 清理
            worker_module.current_task_info = None
            worker_module.is_task_running = False
            await stop_heartbeat()
            
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_heartbeat_frequency(self):
        """测试心跳频率"""
        test_name = "心跳频率测试"
        
        try:
            from src.celery_worker import start_heartbeat, stop_heartbeat
            
            # 记录开始时间和状态
            status_key = f"{CONFIG.Redis.STATUS_KEY_PREFIX}{CONFIG.worker_ip}"
            
            # 启动心跳（2秒间隔）
            await start_heartbeat(self.redis, interval=2)
            
            # 记录多次状态更新的时间
            timestamps = []
            for i in range(3):
                await asyncio.sleep(2.5)  # 等待心跳更新
                status_data = await self.redis.get(status_key)
                if status_data:
                    status = json.loads(status_data)
                    last_update = status.get("last_update_utc")
                    if last_update:
                        timestamps.append(last_update)
            
            await stop_heartbeat()
            
            if len(timestamps) >= 2:
                # 检查时间间隔是否合理（应该接近2秒）
                from datetime import datetime
                time1 = datetime.fromisoformat(timestamps[0].replace('Z', '+00:00'))
                time2 = datetime.fromisoformat(timestamps[1].replace('Z', '+00:00'))
                interval = (time2 - time1).total_seconds()
                
                if 1.5 <= interval <= 3.0:  # 允许一定误差
                    self.record_test_result(test_name, True, f"心跳间隔正常: {interval:.1f}秒")
                else:
                    self.record_test_result(test_name, False, f"心跳间隔异常: {interval:.1f}秒")
            else:
                self.record_test_result(test_name, False, "未收集到足够的时间戳")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def test_code_modifications(self):
        """测试代码修改是否正确"""
        test_name = "代码修改验证"
        
        try:
            # 检查celery_worker.py中的修改
            worker_file = Path("src/celery_worker.py")
            if not worker_file.exists():
                self.record_test_result(test_name, False, "celery_worker.py文件不存在")
                return
            
            with open(worker_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键修改
            checks = [
                ("heartbeat_task变量", "heartbeat_task = None"),
                ("current_task_info变量", "current_task_info = None"),
                ("start_heartbeat函数", "async def start_heartbeat"),
                ("stop_heartbeat函数", "async def stop_heartbeat"),
                ("心跳启动调用", "await start_heartbeat(redis"),
                ("任务信息设置", "current_task_info = task.copy()"),
                ("任务信息清理", "current_task_info = None")
            ]
            
            missing_checks = []
            for check_name, check_pattern in checks:
                if check_pattern not in content:
                    missing_checks.append(check_name)
            
            if not missing_checks:
                self.record_test_result(test_name, True, "所有代码修改都已正确应用")
            else:
                self.record_test_result(test_name, False, f"缺少修改: {', '.join(missing_checks)}")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"异常: {str(e)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("celery_worker.py心跳修复功能测试")
        print("=" * 50)
        
        if not await self.setup():
            print("❌ 测试环境设置失败")
            return False
        
        try:
            # 运行各项测试
            await self.test_code_modifications()
            await self.test_heartbeat_functions()
            await self.test_worker_status_update()
            await self.test_heartbeat_during_task()
            await self.test_heartbeat_frequency()
            
            # 统计测试结果
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result["success"])
            failed_tests = total_tests - passed_tests
            
            print(f"\n测试完成统计:")
            print(f"总测试数: {total_tests}")
            print(f"通过: {passed_tests}")
            print(f"失败: {failed_tests}")
            print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
            
            # 显示失败的测试
            if failed_tests > 0:
                print("\n失败的测试:")
                for result in self.test_results:
                    if not result["success"]:
                        print(f"  - {result['test_name']}: {result['message']}")
            
            return failed_tests == 0
            
        finally:
            await self.cleanup()
    
    def print_detailed_results(self):
        """打印详细测试结果"""
        print("\n" + "="*60)
        print("心跳修复功能详细测试结果")
        print("="*60)
        
        for result in self.test_results:
            status = "PASS" if result["success"] else "FAIL"
            print(f"[{status}] {result['test_name']}")
            if result["message"]:
                print(f"      {result['message']}")
        
        print("="*60)


async def main():
    """主函数"""
    print("SRA项目celery_worker.py心跳修复功能测试")
    print("验证长时间任务执行期间的心跳更新机制")
    print("=" * 60)
    
    tester = HeartbeatTester()
    
    try:
        success = await tester.run_all_tests()
        
        # 打印详细结果
        tester.print_detailed_results()
        
        if success:
            print("\n🎉 所有测试通过！心跳修复功能正常。")
            print("✅ 心跳任务可以正常启动和停止")
            print("✅ 任务执行期间心跳正常更新")
            print("✅ last_update_utc字段正常更新")
            print("✅ 代码修改已正确应用")
            print("\n📝 修复说明:")
            print("- 添加了后台心跳任务，每30秒更新一次worker状态")
            print("- 在长时间任务执行期间，心跳会持续更新last_update_utc")
            print("- 任务开始时设置current_task_info，结束时清理")
            print("- 心跳任务在worker启动时自动启动，退出时自动停止")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查心跳修复实现。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
