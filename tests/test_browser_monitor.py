#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器异常关闭检测功能测试脚本

测试内容：
1. 浏览器状态检测功能
2. 异常关闭检测和告警
3. 浏览器监控任务启动和停止
4. 企业微信告警发送
"""

import asyncio
import json
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as aioredis
from playwright.async_api import async_playwright, Page

from src.conf.config import CONFIG
from src.utils.logger import get_logger
from src.utils.mailer import send_monitoring_alert

logger = get_logger(__name__)


class BrowserMonitorTester:
    """浏览器监控功能测试器"""
    
    def __init__(self):
        self.redis = None
        self.playwright = None
        self.page = None
        self.test_results = []
        
    async def setup(self):
        """初始化测试环境"""
        try:
            # 连接Redis
            self.redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
            logger.info("Redis连接成功")
            
            # 启动Playwright
            self.playwright = await async_playwright().start()
            logger.info("Playwright启动成功")
            
            # 初始化浏览器
            from src.core.browser_manager import init_driver
            self.page = await init_driver()
            logger.info("浏览器初始化成功")
            
            return True
            
        except Exception as e:
            logger.error(f"测试环境初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        try:
            if self.page and not self.page.is_closed():
                await self.page.context.browser.close()
            if self.playwright:
                await self.playwright.stop()
            if self.redis:
                await self.redis.close()
            logger.info("测试环境清理完成")
        except Exception as e:
            logger.error(f"清理测试环境时出错: {e}")
    
    def record_test_result(self, test_name: str, success: bool, message: str):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
    
    async def test_browser_status_check(self):
        """测试浏览器状态检测功能"""
        test_name = "浏览器状态检测功能测试"
        
        try:
            # 导入检测函数
            from src.celery_worker import check_browser_status
            
            # 测试正常状态
            status_info = await check_browser_status(self.page)
            
            if status_info["is_connected"]:
                self.record_test_result(test_name, True, "浏览器状态检测正常")
            else:
                self.record_test_result(test_name, False, f"浏览器状态异常: {status_info['error_details']}")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"测试异常: {str(e)}")
    
    async def test_browser_abnormal_closure_detection(self):
        """测试浏览器异常关闭检测"""
        test_name = "浏览器异常关闭检测测试"
        
        try:
            # 导入检测函数
            from src.celery_worker import detect_browser_abnormal_closure
            
            # 测试正常状态（应该返回False）
            is_abnormal = await detect_browser_abnormal_closure(self.page, "test_account")
            
            if not is_abnormal:
                self.record_test_result(test_name, True, "正常状态检测正确（未检测到异常关闭）")
            else:
                self.record_test_result(test_name, False, "误报异常关闭")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"测试异常: {str(e)}")
    
    async def test_browser_monitor_task(self):
        """测试浏览器监控任务"""
        test_name = "浏览器监控任务测试"
        
        try:
            # 导入监控函数
            from src.celery_worker import start_browser_monitor, stop_browser_monitor
            
            # 启动监控任务
            await start_browser_monitor(self.page, self.redis, interval=5)
            logger.info("浏览器监控任务已启动")
            
            # 等待一段时间让监控任务运行
            await asyncio.sleep(10)
            
            # 停止监控任务
            await stop_browser_monitor()
            logger.info("浏览器监控任务已停止")
            
            self.record_test_result(test_name, True, "浏览器监控任务启动和停止正常")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"测试异常: {str(e)}")
    
    async def test_wechat_alert_sending(self):
        """测试企业微信告警发送"""
        test_name = "企业微信告警发送测试"
        
        try:
            # 发送测试告警
            test_alert_content = f"""
SRA Worker 浏览器监控测试告警

时间: {datetime.now().isoformat()}
用户ID: {CONFIG.worker_ip}
测试账户: test_account

这是一个测试告警消息，用于验证浏览器异常关闭检测功能。

测试详情:
- 浏览器状态检测: 正常
- 异常关闭检测: 正常
- 监控任务: 正常
- 告警发送: 测试中

如果收到此消息，说明告警功能正常工作。
"""
            
            success = await send_monitoring_alert(
                title=f"SRA Worker 浏览器监控测试 - {CONFIG.worker_ip}",
                error_details=test_alert_content,
                page=self.page,
                severity="info"
            )
            
            if success:
                self.record_test_result(test_name, True, "企业微信告警发送成功")
            else:
                self.record_test_result(test_name, False, "企业微信告警发送失败")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"测试异常: {str(e)}")
    
    async def test_simulated_browser_closure(self):
        """测试模拟浏览器关闭场景"""
        test_name = "模拟浏览器关闭测试"
        
        try:
            # 创建一个新的浏览器实例用于测试
            test_browser = await self.playwright.chromium.launch()
            test_context = await test_browser.new_context()
            test_page = await test_context.new_page()
            
            # 关闭浏览器
            await test_browser.close()
            
            # 等待一下确保浏览器完全关闭
            await asyncio.sleep(2)
            
            # 测试检测函数
            from src.celery_worker import detect_browser_abnormal_closure
            
            is_abnormal = await detect_browser_abnormal_closure(test_page, "test_account")
            
            if is_abnormal:
                self.record_test_result(test_name, True, "成功检测到浏览器异常关闭")
            else:
                self.record_test_result(test_name, False, "未能检测到浏览器异常关闭")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"测试异常: {str(e)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始浏览器监控功能测试...")
        print("=" * 60)
        
        # 初始化测试环境
        if not await self.setup():
            print("❌ 测试环境初始化失败，退出测试")
            return
        
        try:
            # 运行各项测试
            await self.test_browser_status_check()
            await asyncio.sleep(1)
            
            await self.test_browser_abnormal_closure_detection()
            await asyncio.sleep(1)
            
            await self.test_browser_monitor_task()
            await asyncio.sleep(1)
            
            await self.test_wechat_alert_sending()
            await asyncio.sleep(1)
            
            await self.test_simulated_browser_closure()
            await asyncio.sleep(1)
            
        finally:
            # 清理测试环境
            await self.cleanup()
        
        # 输出测试结果汇总
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        print("=" * 60)
        
        success_count = sum(1 for result in self.test_results if result["success"])
        total_count = len(self.test_results)
        
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test_name']}")
            print(f"   结果: {result['message']}")
            print(f"   时间: {result['timestamp']}")
            print()
        
        print(f"总计: {success_count}/{total_count} 项测试通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！浏览器监控功能正常工作。")
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
        
        return success_count == total_count


async def main():
    """主函数"""
    tester = BrowserMonitorTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ 浏览器监控功能测试完成，所有功能正常！")
    else:
        print("\n❌ 浏览器监控功能测试完成，存在问题需要修复。")


if __name__ == "__main__":
    asyncio.run(main())
