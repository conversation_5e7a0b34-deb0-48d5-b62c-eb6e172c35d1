#!/usr/bin/env python3
"""
发送获取简历详情任务到Redis队列的脚本
"""
import asyncio
import json
import sys
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import redis.asyncio as aioredis
from src.conf.config import CONFIG


async def send_resume_task(url: str):
    """发送获取简历详情的任务"""
    # 连接Redis
    redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
    
    try:
        # 生成任务ID
        task_id = f"resume_task_{uuid.uuid4().hex[:8]}"
        result_channel = f"result_{task_id}"
        
        # 构建任务
        task = {
            "id": task_id,
            "action": "get_resume_detail",
            "payload": {
                "url": url
            },
            "result_channel": result_channel,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        print(f"发送任务: {task_id}")
        print(f"URL: {url}")
        print(f"结果频道: {result_channel}")
        
        # 发送任务到队列
        queue_name = f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.worker_ip}"
        await redis.rpush(queue_name, json.dumps(task))
        print(f"任务已发送到队列: {queue_name}")
        
        # 订阅结果频道
        pubsub = redis.pubsub()
        await pubsub.subscribe(result_channel)
        
        print("等待任务结果...")
        
        # 等待结果，最多等待60秒
        timeout = 60
        start_time = asyncio.get_event_loop().time()
        
        async for message in pubsub.listen():
            if message['type'] == 'message':
                try:
                    result = json.loads(message['data'])
                    print(f"\n收到任务结果:")
                    print(json.dumps(result, ensure_ascii=False, indent=2))
                    
                    if result.get('status') == 'success':
                        zp_data = result.get('data', {})
                        print(f"\n简历信息:")
                        print(f"姓名: {zp_data.get('geekName', '未知')}")
                        print(f"年龄: {zp_data.get('ageDesc', '未知')}")
                        print(f"工作年限: {zp_data.get('workYearDesc', '未知')}")
                        print(f"期望薪资: {zp_data.get('salaryDesc', '未知')}")
                        print(f"简历描述: {zp_data.get('geekDesc', '未知')[:100]}...")
                    
                    break
                    
                except json.JSONDecodeError as e:
                    print(f"解析结果失败: {e}")
                    print(f"原始数据: {message['data']}")
            
            # 检查超时
            if asyncio.get_event_loop().time() - start_time > timeout:
                print("等待超时，任务可能失败")
                break
        
        await pubsub.unsubscribe(result_channel)
        
    except Exception as e:
        print(f"发送任务失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await redis.close()


async def send_multiple_tasks():
    """发送多个任务测试并发处理"""
    urls = [
        "https://www.zhipin.com/web/boss/resume/share?shareId=35910040",
        "https://www.zhipin.com/web/boss/resume/share?shareId=35910041",
        "https://www.zhipin.com/web/boss/resume/share?shareId=35910042"
    ]
    
    print("发送多个任务测试并发处理...")
    
    # 连接Redis
    redis = await aioredis.from_url(CONFIG.Redis.URL, decode_responses=True)
    
    try:
        tasks = []
        result_channels = []
        
        # 创建多个任务
        for i, url in enumerate(urls):
            task_id = f"resume_task_{i+1}_{uuid.uuid4().hex[:8]}"
            result_channel = f"result_{task_id}"
            
            task = {
                "id": task_id,
                "action": "get_resume_detail",
                "payload": {
                    "url": url
                },
                "result_channel": result_channel,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            tasks.append(task)
            result_channels.append(result_channel)
        
        # 快速发送所有任务
        queue_name = f"{CONFIG.Redis.TASK_QUEUE_PREFIX}{CONFIG.worker_ip}"
        for task in tasks:
            await redis.rpush(queue_name, json.dumps(task))
            print(f"发送任务: {task['id']}")
        
        # 订阅所有结果频道
        pubsub = redis.pubsub()
        for channel in result_channels:
            await pubsub.subscribe(channel)
        
        print(f"等待 {len(tasks)} 个任务的结果...")
        
        received_results = 0
        timeout = 120  # 2分钟超时
        start_time = asyncio.get_event_loop().time()
        
        async for message in pubsub.listen():
            if message['type'] == 'message':
                try:
                    result = json.loads(message['data'])
                    received_results += 1
                    
                    print(f"\n收到第 {received_results} 个结果:")
                    print(f"任务ID: {result.get('id')}")
                    print(f"状态: {result.get('status')}")
                    
                    if result.get('status') == 'success':
                        zp_data = result.get('data', {})
                        print(f"姓名: {zp_data.get('geekName', '未知')}")
                    elif result.get('status') == 'error':
                        print(f"错误: {result.get('message')}")
                    
                    # 如果收到所有结果，退出
                    if received_results >= len(tasks):
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"解析结果失败: {e}")
            
            # 检查超时
            if asyncio.get_event_loop().time() - start_time > timeout:
                print("等待超时")
                break
        
        for channel in result_channels:
            await pubsub.unsubscribe(channel)
        
        print(f"\n测试完成，收到 {received_results}/{len(tasks)} 个结果")
        
    except Exception as e:
        print(f"多任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await redis.close()


def main():
    print("Boss直聘简历详情获取工具")
    print("=" * 50)
    
    print("选择测试模式:")
    print("1. 发送单个简历详情获取任务")
    print("2. 发送多个任务测试并发处理")
    print("3. 使用自定义URL")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 默认测试URL
        url = "https://www.zhipin.com/web/boss/resume/share?shareId=35910040"
        print(f"使用默认URL: {url}")
        asyncio.run(send_resume_task(url))
        
    elif choice == "2":
        asyncio.run(send_multiple_tasks())
        
    elif choice == "3":
        url = input("请输入简历分享URL: ").strip()
        if url:
            asyncio.run(send_resume_task(url))
        else:
            print("URL不能为空")
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
