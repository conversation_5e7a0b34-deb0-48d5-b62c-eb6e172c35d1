#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动加载Cookie功能使用示例
"""

import asyncio
import os
import sys

# 添加src目录到Python路径
sys.path.append('./src')

async def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    try:
        # 导入browser_manager（注意：这里只是示例，实际使用时需要完整环境）
        # from src.core.browser_manager import init_driver
        
        print("1. 自动加载Cookie（推荐方式）:")
        print("   page = await init_driver()")
        print("   # 系统会自动检测并加载最新的cookie文件")
        
        print("\n2. 禁用自动加载:")
        print("   page = await init_driver(auto_load_cookies=False)")
        print("   # 使用无cookie状态启动浏览器")
        
        print("\n3. 检查可用Cookie文件:")
        print("   from src.core.browser_manager import find_available_user_cookies")
        print("   available_cookies = find_available_user_cookies()")
        print("   # 返回所有可用cookie文件的详细信息")
        
    except Exception as e:
        print(f"示例执行出错: {e}")

def example_cookie_management():
    """Cookie文件管理示例"""
    print("\n=== Cookie文件管理示例 ===")
    
    # 模拟查找cookie文件
    import glob
    
    data_dir = "./data"
    pattern = os.path.join(data_dir, "user_*.json")
    cookie_files = glob.glob(pattern)
    
    print(f"在 {data_dir} 目录下找到 {len(cookie_files)} 个cookie文件:")
    
    for i, file_path in enumerate(cookie_files, 1):
        filename = os.path.basename(file_path)
        account_name = filename.replace("user_", "").replace(".json", "")
        file_size = os.path.getsize(file_path)
        
        print(f"  {i}. 文件: {filename}")
        print(f"     账号: {account_name}")
        print(f"     大小: {file_size} 字节")
        print(f"     路径: {file_path}")

def example_environment_setup():
    """环境变量设置示例"""
    print("\n=== 环境变量设置示例 ===")
    
    # 显示当前环境变量
    current_account = os.environ.get('account_name')
    if current_account:
        print(f"当前账号环境变量: {current_account}")
    else:
        print("当前未设置账号环境变量")
    
    print("\n自动加载Cookie后，系统会自动设置以下环境变量:")
    print("- account_name: 从cookie文件名提取的账号名")
    print("- 其他模块可以通过 os.environ.get('account_name') 获取")

def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    print("常见错误情况及处理:")
    print("1. 无Cookie文件:")
    print("   - 系统会输出: '未找到用户cookie文件，跳过自动加载'")
    print("   - 浏览器会以无cookie状态启动")
    
    print("\n2. Cookie文件格式错误:")
    print("   - 系统会跳过无效文件")
    print("   - 尝试加载其他有效文件")
    
    print("\n3. 多个Cookie文件:")
    print("   - 系统会自动选择最新修改的文件")
    print("   - 输出: '找到多个cookie文件，选择最新的: xxx'")

def example_best_practices():
    """最佳实践示例"""
    print("\n=== 最佳实践示例 ===")
    
    print("1. 文件命名规范:")
    print("   ✅ 正确: user_张三.json, user_李四.json")
    print("   ❌ 错误: zhang_san.json, user.json")
    
    print("\n2. 文件存放位置:")
    print("   ✅ 正确: ./data/user_账号名.json")
    print("   ❌ 错误: ./user_账号名.json, ./cookies/user_账号名.json")
    
    print("\n3. 安全考虑:")
    print("   - 不要将cookie文件提交到Git仓库")
    print("   - 定期更新cookie以保持有效性")
    print("   - 注意文件权限，避免泄露")
    
    print("\n4. 调试技巧:")
    print("   - 使用 find_available_user_cookies() 查看可用文件")
    print("   - 检查日志输出确认加载状态")
    print("   - 测试时可以禁用自动加载: auto_load_cookies=False")

async def example_integration():
    """集成使用示例"""
    print("\n=== 集成使用示例 ===")
    
    print("在现有项目中集成自动加载Cookie:")
    
    print("\n原有代码:")
    print("""
    # 手动指定账号和cookie
    account_name = "张三"
    os.environ['account_name'] = account_name
    page = await init_driver()
    # 然后手动加载cookie...
    """)
    
    print("新代码:")
    print("""
    # 自动检测和加载
    page = await init_driver()  # 就这么简单！
    # 系统会自动:
    # 1. 检测可用的cookie文件
    # 2. 选择最新的文件
    # 3. 提取账号名并设置环境变量
    # 4. 加载cookie到浏览器
    """)

def main():
    """主函数"""
    print("自动加载Cookie功能使用示例")
    print("=" * 50)
    
    # 基本使用示例
    asyncio.run(example_basic_usage())
    
    # Cookie文件管理
    example_cookie_management()
    
    # 环境变量设置
    example_environment_setup()
    
    # 错误处理
    example_error_handling()
    
    # 最佳实践
    example_best_practices()
    
    # 集成使用
    asyncio.run(example_integration())
    
    print("\n" + "=" * 50)
    print("🎉 示例演示完成!")
    print("\n快速开始:")
    print("1. 确保在 ./data/ 目录下有 user_*.json 格式的cookie文件")
    print("2. 调用 await init_driver() 即可自动加载最新的cookie")
    print("3. 查看日志确认加载状态")
    print("\n更多详情请参考: AUTO_LOAD_COOKIES_FEATURE.md")

if __name__ == "__main__":
    main()
