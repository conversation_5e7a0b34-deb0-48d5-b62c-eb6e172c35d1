#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动加载cookie功能的脚本
"""

import asyncio
import os
import sys
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.append('./src')

from src.core.browser_manager import find_available_user_cookies, auto_load_user_cookies
from src.utils.logger import get_logger

logger = get_logger(__name__)

class MockContext:
    """模拟Playwright浏览器上下文"""
    
    def __init__(self):
        self.cookies = []
    
    async def add_cookies(self, cookies):
        """模拟添加cookies"""
        self.cookies.extend(cookies)
        logger.info(f"模拟添加了 {len(cookies)} 个cookie到浏览器上下文")

async def test_find_available_cookies():
    """测试查找可用cookie文件功能"""
    logger.info("=== 测试查找可用cookie文件 ===")
    
    available_cookies = find_available_user_cookies()
    
    if available_cookies:
        logger.info(f"找到 {len(available_cookies)} 个可用的cookie文件:")
        for i, cookie_info in enumerate(available_cookies, 1):
            mtime = datetime.fromtimestamp(cookie_info["modified_time"])
            logger.info(f"  {i}. 账号: {cookie_info['account_name']}")
            logger.info(f"     文件: {cookie_info['file_path']}")
            logger.info(f"     Cookie数量: {cookie_info['cookie_count']}")
            logger.info(f"     修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info("")
    else:
        logger.warning("未找到任何可用的cookie文件")
    
    return available_cookies

async def test_auto_load_cookies():
    """测试自动加载cookie功能"""
    logger.info("=== 测试自动加载cookie功能 ===")
    
    # 创建模拟的浏览器上下文
    mock_context = MockContext()
    
    # 保存原始环境变量
    original_account_name = os.environ.get('account_name')
    
    try:
        # 测试自动加载
        await auto_load_user_cookies(mock_context)
        
        # 检查结果
        logger.info(f"加载后的cookie数量: {len(mock_context.cookies)}")
        
        if mock_context.cookies:
            logger.info("成功加载的cookie示例:")
            for i, cookie in enumerate(mock_context.cookies[:3]):  # 只显示前3个
                logger.info(f"  {i+1}. {cookie.get('name', 'unknown')} = {cookie.get('value', 'unknown')[:20]}...")
        
        # 检查环境变量
        current_account_name = os.environ.get('account_name')
        if current_account_name:
            logger.info(f"设置的账号名环境变量: {current_account_name}")
        else:
            logger.warning("未设置账号名环境变量")
            
    finally:
        # 恢复原始环境变量
        if original_account_name:
            os.environ['account_name'] = original_account_name
        elif 'account_name' in os.environ:
            del os.environ['account_name']

def test_cookie_file_validation():
    """测试cookie文件验证功能"""
    logger.info("=== 测试cookie文件验证 ===")
    
    available_cookies = find_available_user_cookies()
    
    if not available_cookies:
        logger.warning("没有可用的cookie文件进行验证测试")
        return
    
    # 验证第一个cookie文件
    cookie_info = available_cookies[0]
    file_path = cookie_info["file_path"]
    
    logger.info(f"验证cookie文件: {file_path}")
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            login_data = json.load(f)
        
        cookies = login_data.get("cookies", [])
        
        logger.info(f"文件包含 {len(cookies)} 个cookie")
        
        # 验证cookie结构
        required_fields = ["name", "value", "domain"]
        valid_cookies = 0
        
        for cookie in cookies:
            if all(field in cookie for field in required_fields):
                valid_cookies += 1
            else:
                logger.warning(f"无效的cookie结构: {cookie}")
        
        logger.info(f"有效的cookie数量: {valid_cookies}/{len(cookies)}")
        
        # 显示域名统计
        domains = {}
        for cookie in cookies:
            domain = cookie.get("domain", "unknown")
            domains[domain] = domains.get(domain, 0) + 1
        
        logger.info("Cookie域名分布:")
        for domain, count in domains.items():
            logger.info(f"  {domain}: {count} 个cookie")
            
    except Exception as e:
        logger.error(f"验证cookie文件失败: {e}")

async def main():
    """主测试函数"""
    logger.info("开始测试自动加载cookie功能")
    logger.info("=" * 50)
    
    # 测试1: 查找可用cookie文件
    available_cookies = await test_find_available_cookies()
    
    # 测试2: 验证cookie文件
    test_cookie_file_validation()
    
    # 测试3: 自动加载cookie
    await test_auto_load_cookies()
    
    logger.info("=" * 50)
    logger.info("测试完成")
    
    # 总结
    if available_cookies:
        logger.info(f"✅ 找到 {len(available_cookies)} 个可用的cookie文件")
        logger.info("✅ 自动加载cookie功能可以正常工作")
        logger.info("\n推荐使用方式:")
        logger.info("1. 调用 init_driver() 时会自动加载最新的cookie文件")
        logger.info("2. 可以通过 init_driver(auto_load_cookies=False) 禁用自动加载")
        logger.info("3. 使用 find_available_user_cookies() 查看所有可用的cookie文件")
    else:
        logger.warning("❌ 未找到任何cookie文件")
        logger.info("请确保在 ./data/ 目录下有 user_*.json 格式的cookie文件")

if __name__ == "__main__":
    asyncio.run(main())
