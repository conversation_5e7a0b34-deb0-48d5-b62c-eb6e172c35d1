"""
缓存管理器测试
验证时间敏感缓存功能是否按预期工作
"""
import unittest
import time
from datetime import datetime, timedelta
from unittest.mock import patch

from src.utils.cache_manager import TimeSensitiveCacheManager


class TestTimeSensitiveCacheManager(unittest.TestCase):
    
    def setUp(self):
        """测试前准备"""
        self.cache_manager = TimeSensitiveCacheManager()
        self.test_key = "test_key"
        self.test_value = {"data": "test_data", "timestamp": "2024-01-01"}
    
    def tearDown(self):
        """测试后清理"""
        self.cache_manager.clear_all_cache()
    
    def test_basic_cache_operations(self):
        """测试基本缓存操作"""
        # 测试设置和获取缓存
        self.cache_manager.set_cache(self.test_key, self.test_value, ttl_minutes=1)
        result = self.cache_manager.get_cache(self.test_key)
        self.assertEqual(result, self.test_value)
        
        # 测试缓存信息
        cache_info = self.cache_manager.get_cache_info(self.test_key)
        self.assertIsNotNone(cache_info)
        self.assertEqual(cache_info['key'], self.test_key)
        self.assertEqual(cache_info['ttl_minutes'], 1)
        self.assertFalse(cache_info['is_expired'])
        
        # 测试清除缓存
        self.assertTrue(self.cache_manager.clear_cache(self.test_key))
        self.assertIsNone(self.cache_manager.get_cache(self.test_key))
    
    def test_cache_expiration(self):
        """测试缓存过期"""
        # 设置一个很短的TTL
        self.cache_manager.set_cache(self.test_key, self.test_value, ttl_minutes=0.01)  # 0.6秒
        
        # 立即获取应该成功
        result = self.cache_manager.get_cache(self.test_key)
        self.assertEqual(result, self.test_value)
        
        # 等待过期
        time.sleep(1)
        
        # 过期后应该返回None
        result = self.cache_manager.get_cache(self.test_key)
        self.assertIsNone(result)
    
    def test_night_mode_detection(self):
        """测试夜间模式检测"""
        # 测试白天时间（10:00）
        morning_time = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
        self.assertFalse(self.cache_manager._is_night_mode(morning_time))
        
        # 测试夜间时间（23:00）
        night_time = datetime.now().replace(hour=23, minute=0, second=0, microsecond=0)
        self.assertTrue(self.cache_manager._is_night_mode(night_time))
        
        # 测试早晨时间（06:00）
        early_morning_time = datetime.now().replace(hour=6, minute=0, second=0, microsecond=0)
        self.assertTrue(self.cache_manager._is_night_mode(early_morning_time))
    
    def test_last_evening_cutoff(self):
        """测试最近一次晚上22点的计算"""
        # 测试当前时间是今天23点
        current_time = datetime.now().replace(hour=23, minute=30, second=0, microsecond=0)
        cutoff = self.cache_manager._get_last_evening_cutoff(current_time)
        expected = current_time.replace(hour=22, minute=0, second=0, microsecond=0)
        self.assertEqual(cutoff, expected)
        
        # 测试当前时间是今天10点
        current_time = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
        cutoff = self.cache_manager._get_last_evening_cutoff(current_time)
        expected = (current_time - timedelta(days=1)).replace(hour=22, minute=0, second=0, microsecond=0)
        self.assertEqual(cutoff, expected)
    
    @patch('src.utils.cache_manager.TimeSensitiveCacheManager._get_current_time')
    def test_night_mode_cache_behavior(self, mock_current_time):
        """测试夜间模式缓存行为"""
        # 模拟在21:30设置缓存
        evening_time = datetime.now().replace(hour=21, minute=30, second=0, microsecond=0)
        mock_current_time.return_value = evening_time
        
        self.cache_manager.set_cache(
            self.test_key, 
            self.test_value, 
            ttl_minutes=60,  # 1小时TTL
            enable_night_mode=True
        )
        
        # 在22:30（夜间模式）获取缓存，应该返回21:30设置的缓存
        night_time = evening_time.replace(hour=22, minute=30)
        mock_current_time.return_value = night_time
        
        result = self.cache_manager.get_cache(self.test_key)
        self.assertEqual(result, self.test_value)
        
        # 在次日07:00（仍在夜间模式）获取缓存，应该仍然返回21:30设置的缓存
        next_morning = night_time.replace(hour=7) + timedelta(days=1)
        mock_current_time.return_value = next_morning
        
        result = self.cache_manager.get_cache(self.test_key)
        self.assertEqual(result, self.test_value)
        
        # 在次日09:00（白天模式）获取缓存，如果还在TTL内应该返回缓存
        next_day_morning = night_time.replace(hour=9) + timedelta(days=1)
        mock_current_time.return_value = next_day_morning
        
        result = self.cache_manager.get_cache(self.test_key)
        self.assertIsNone(result)  # 应该过期了（超过1小时TTL）
    
    @patch('src.utils.cache_manager.TimeSensitiveCacheManager._get_current_time')
    def test_night_mode_cache_created_after_22(self, mock_current_time):
        """测试在22点后创建的夜间模式缓存"""
        # 模拟在23:00设置缓存
        night_time = datetime.now().replace(hour=23, minute=0, second=0, microsecond=0)
        mock_current_time.return_value = night_time
        
        self.cache_manager.set_cache(
            self.test_key,
            self.test_value,
            ttl_minutes=600,  # 10小时TTL，确保到次日7点还没过期
            enable_night_mode=True
        )

        # 在23:30获取缓存，应该成功
        later_night = night_time.replace(minute=30)
        mock_current_time.return_value = later_night

        result = self.cache_manager.get_cache(self.test_key)
        self.assertEqual(result, self.test_value)

        # 在次日07:00获取缓存，应该成功（仍在TTL内且在夜间模式）
        next_morning = night_time.replace(hour=7) + timedelta(days=1)
        mock_current_time.return_value = next_morning

        result = self.cache_manager.get_cache(self.test_key)
        self.assertEqual(result, self.test_value)
    
    def test_cleanup_expired_cache(self):
        """测试清理过期缓存"""
        # 设置多个缓存，其中一些过期
        self.cache_manager.set_cache("key1", "value1", ttl_minutes=60)  # 不过期
        self.cache_manager.set_cache("key2", "value2", ttl_minutes=0.01)  # 很快过期
        self.cache_manager.set_cache("key3", "value3", ttl_minutes=0.01)  # 很快过期
        
        # 等待部分缓存过期
        time.sleep(1)
        
        # 清理过期缓存
        cleaned_count = self.cache_manager.cleanup_expired_cache()
        self.assertEqual(cleaned_count, 2)  # 应该清理了2个过期缓存
        
        # 验证未过期的缓存仍然存在
        self.assertIsNotNone(self.cache_manager.get_cache("key1"))
        self.assertIsNone(self.cache_manager.get_cache("key2"))
        self.assertIsNone(self.cache_manager.get_cache("key3"))


def run_cache_tests():
    """运行缓存测试"""
    print("开始运行缓存管理器测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestTimeSensitiveCacheManager)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有缓存测试通过！")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False


if __name__ == "__main__":
    run_cache_tests()
