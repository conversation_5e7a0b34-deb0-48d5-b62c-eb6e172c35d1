"""
缓存功能使用示例
演示如何使用周报接口的缓存功能和缓存管理API
"""
import asyncio
import json
import time
from datetime import datetime

import aiohttp


class CacheUsageExample:
    """缓存功能使用示例"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    async def call_weekly_report_api(self):
        """调用周报接口"""
        url = f"{self.base_url}/agent/weeklyReport/recruitDataCenter/get"
        
        # 模拟请求数据
        payload = {
            # 根据实际的WeeklyReportRecruitDataCenterGetItem模型填写
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    print(f"API调用失败: {response.status}")
                    return None
    
    async def get_cache_status(self):
        """获取缓存系统状态"""
        url = f"{self.base_url}/cache/status"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    print(f"获取缓存状态失败: {response.status}")
                    return None
    
    async def get_weekly_report_cache_info(self):
        """获取周报缓存信息"""
        url = f"{self.base_url}/cache/weekly-report/info"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    print(f"获取周报缓存信息失败: {response.status}")
                    return None
    
    async def clear_weekly_report_cache(self):
        """清除周报缓存"""
        url = f"{self.base_url}/cache/weekly-report"
        
        async with aiohttp.ClientSession() as session:
            async with session.delete(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    print(f"清除周报缓存失败: {response.status}")
                    return None
    
    async def cleanup_expired_cache(self):
        """清理过期缓存"""
        url = f"{self.base_url}/cache/cleanup"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    print(f"清理过期缓存失败: {response.status}")
                    return None
    
    async def demo_cache_behavior(self):
        """演示缓存行为"""
        print("=== 缓存功能演示 ===\n")
        
        # 1. 查看初始缓存状态
        print("1. 查看初始缓存状态:")
        status = await self.get_cache_status()
        if status:
            result = status.get('result', {})
            print(f"   当前时间: {result.get('current_time')}")
            print(f"   是否夜间模式: {result.get('is_night_mode')}")
            print(f"   最近22点: {result.get('last_evening_cutoff')}")
            print(f"   下次8点: {result.get('next_morning_8am')}")
            print(f"   Worker ID: {result.get('worker_id')}")
            
            weekly_cache = result.get('weekly_report_cache')
            if weekly_cache:
                print(f"   周报缓存存在: {weekly_cache}")
            else:
                print("   周报缓存不存在")
        print()
        
        # 2. 第一次调用周报接口
        print("2. 第一次调用周报接口:")
        start_time = time.time()
        result1 = await self.call_weekly_report_api()
        end_time = time.time()
        
        if result1:
            print(f"   响应时间: {(end_time - start_time):.2f}秒")
            print(f"   消息: {result1.get('message')}")
            print(f"   是否来自缓存: {'缓存' in result1.get('message', '')}")
        print()
        
        # 3. 立即第二次调用（应该命中缓存）
        print("3. 立即第二次调用（应该命中缓存）:")
        start_time = time.time()
        result2 = await self.call_weekly_report_api()
        end_time = time.time()
        
        if result2:
            print(f"   响应时间: {(end_time - start_time):.2f}秒")
            print(f"   消息: {result2.get('message')}")
            print(f"   是否来自缓存: {'缓存' in result2.get('message', '')}")
        print()
        
        # 4. 查看缓存信息
        print("4. 查看缓存信息:")
        cache_info = await self.get_weekly_report_cache_info()
        if cache_info and cache_info.get('code') == 0:
            info = cache_info.get('result', {})
            print(f"   缓存键: {info.get('key')}")
            print(f"   创建时间: {info.get('created_at')}")
            print(f"   过期时间: {info.get('expire_time')}")
            print(f"   TTL: {info.get('ttl_minutes')}分钟")
            print(f"   夜间模式: {info.get('enable_night_mode')}")
            print(f"   是否过期: {info.get('is_expired')}")
            print(f"   当前是否夜间: {info.get('is_night_mode')}")
        else:
            print("   缓存不存在或获取失败")
        print()
        
        # 5. 清除缓存
        print("5. 清除缓存:")
        clear_result = await self.clear_weekly_report_cache()
        if clear_result:
            print(f"   结果: {clear_result.get('message')}")
        print()
        
        # 6. 清除后再次调用（应该重新获取数据）
        print("6. 清除缓存后再次调用:")
        start_time = time.time()
        result3 = await self.call_weekly_report_api()
        end_time = time.time()
        
        if result3:
            print(f"   响应时间: {(end_time - start_time):.2f}秒")
            print(f"   消息: {result3.get('message')}")
            print(f"   是否来自缓存: {'缓存' in result3.get('message', '')}")
        print()
        
        # 7. 清理过期缓存
        print("7. 清理过期缓存:")
        cleanup_result = await self.cleanup_expired_cache()
        if cleanup_result:
            result = cleanup_result.get('result', {})
            print(f"   清理结果: {cleanup_result.get('message')}")
            print(f"   清理数量: {result.get('cleaned_count', 0)}")
        print()
        
        print("=== 演示完成 ===")
    
    async def demo_night_mode_simulation(self):
        """演示夜间模式（需要手动修改时间进行测试）"""
        print("=== 夜间模式演示 ===")
        print("注意: 夜间模式需要在实际的夜间时段（22:00-08:00）进行测试")
        print("或者可以修改系统时间来模拟夜间模式")
        print()
        
        # 查看当前状态
        status = await self.get_cache_status()
        if status:
            result = status.get('result', {})
            is_night = result.get('is_night_mode', False)
            current_time = result.get('current_time', '')
            
            print(f"当前时间: {current_time}")
            print(f"是否夜间模式: {is_night}")
            
            if is_night:
                print("✅ 当前处于夜间模式")
                print("在此模式下，22点前创建的缓存会一直有效到次日8点")
            else:
                print("⏰ 当前处于白天模式")
                print("在此模式下，缓存按正常30分钟TTL处理")
        
        print("=== 夜间模式演示完成 ===")


async def main():
    """主函数"""
    example = CacheUsageExample()
    
    print("周报接口缓存功能使用示例")
    print("确保FastAPI服务正在运行在 http://localhost:8000")
    print()
    
    try:
        # 演示基本缓存行为
        await example.demo_cache_behavior()
        
        print("\n" + "="*50 + "\n")
        
        # 演示夜间模式
        await example.demo_night_mode_simulation()
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        print("请确保:")
        print("1. FastAPI服务正在运行")
        print("2. Redis服务正在运行")
        print("3. Worker服务正在运行")


if __name__ == "__main__":
    asyncio.run(main())
