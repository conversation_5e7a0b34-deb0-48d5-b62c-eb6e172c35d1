# SRA Worker 浏览器异常关闭检测功能实现总结

## 实现概述

✅ **实现状态**: 已完成  
📅 **完成时间**: 2025年8月8日  
🎯 **实现目标**: 在worker运行时检测playwright浏览器被异常关闭的情况，当被异常关闭时发送企业微信告警  

## 核心功能实现

### 1. 🔍 **浏览器状态检测机制**

#### 检测函数
```python
async def check_browser_status(page: Page) -> dict:
    """检查浏览器状态，返回详细状态信息"""
    status_info = {
        "is_connected": False,
        "is_closed": False,
        "context_closed": False,
        "browser_closed": False,
        "page_closed": False,
        "url": "unknown",
        "error_details": ""
    }
    # 检测逻辑...
    return status_info
```

#### 检测项目
- **页面状态**: `page.is_closed()`
- **浏览器连接**: `page.context.browser.is_connected()`
- **URL访问测试**: 通过获取当前URL验证连接性

### 2. 🚨 **异常关闭检测和告警**

#### 检测函数

```python
async def detect_browser_abnormal_closure(page: Page, account_name: str = None) -> bool:
    """检测浏览器是否异常关闭"""
    status_info = await check_browser_status(page)

    if status_info["is_connected"]:
        return False  # 正常状态

    # 发送企业微信告警
    await send_monitoring_alert(
        title=f"SRA Worker 浏览器异常关闭 - {CONFIG.worker_ip}",
        error_details=error_details,
        page=page,
        severity="critical"
    )
    return True
```

#### 告警内容
- **时间戳**: 异常发生时间
- **用户信息**: 用户ID和账户名称
- **状态详情**: 浏览器各组件状态
- **错误原因**: 具体的错误信息
- **建议操作**: 处理建议和步骤
- **页面截图**: 自动附带当前页面截图

### 3. 🔄 **浏览器监控任务**

#### 监控任务启动
```python
async def start_browser_monitor(page: Page, redis: aioredis.Redis, interval: int = 30):
    """启动浏览器监控任务"""
    async def browser_monitor_loop():
        while True:
            if await detect_browser_abnormal_closure(page):
                # 进入管理模式
                await _enter_management_mode(...)
            await asyncio.sleep(interval)
```

#### 监控特点
- **定期检测**: 默认30秒间隔
- **自动告警**: 检测到异常立即发送告警
- **管理模式**: 异常后自动进入管理模式
- **重启支持**: 支持通过管理指令重启浏览器

### 4. 🔧 **集成到Worker主循环**

#### 启动集成
```python
# 在browser_manager()中启动监控
await start_browser_monitor(page, redis, interval=30)
```

#### 停止集成
```python
# 在异常处理和管理模式中停止监控
await stop_browser_monitor()
```

## 技术实现细节

### 1. **状态检测逻辑**
```python
try:
    # 检查页面是否关闭
    if page.is_closed():
        status_info["page_closed"] = True
        status_info["error_details"] = "页面已关闭"
        return status_info
        
    # 检查浏览器连接状态
    if not page.context.browser.is_connected():
        status_info["is_connected"] = False
        status_info["error_details"] = "浏览器连接已断开"
        return status_info
        
    # 尝试获取当前URL（作为连接性测试）
    current_url = page.url
    status_info["url"] = current_url
    status_info["is_connected"] = True
    
except Exception as e:
    status_info["error_details"] = f"浏览器状态检查异常: {str(e)}"
```

### 2. **告警消息格式**
```markdown
## 🚨 SRA监控告警

**告警时间**: 2025-08-08 11:02:35
**严重程度**: CRITICAL
**告警标题**: SRA Worker 浏览器异常关闭 - user_X

### 错误详情
```
SRA Worker 检测到浏览器异常关闭

时间: 2025-08-08T11:02:35
用户ID: user_X
账户: demo_account

浏览器状态信息:
- 页面关闭: True
- 上下文关闭: False
- 浏览器关闭: False
- 连接状态: False
- 最后URL: https://www.zhipin.com/

错误详情: 页面已关闭

可能的原因:
1. 浏览器进程被意外终止
2. 网络连接异常导致浏览器断开
3. 系统资源不足导致浏览器崩溃
4. 反爬虫机制触发浏览器关闭

建议操作:
1. 检查系统资源使用情况
2. 验证网络连接稳定性
3. 检查是否有反爬虫检测
4. 重启Worker服务
```
```

### 3. **管理模式集成**
```python
# 在_enter_management_mode中处理浏览器重启
elif command == "restart":
    logger.info("[管理指令] 执行浏览器重启...")
    new_page = await initialize_browser()
    if new_page:
        await update_worker_status(redis, "idle")
        return new_page  # 返回新的页面对象
```

## 测试验证

### 1. **功能测试**
```bash
# 运行完整功能测试
python3 tests/test_browser_monitor.py
```

### 2. **演示脚本**
```bash
# 运行功能演示
python3 tests/demo_browser_monitor.py
```

### 3. **测试结果**
```
✅ 浏览器状态检测功能测试
✅ 浏览器异常关闭检测测试
✅ 浏览器监控任务测试
✅ 企业微信告警发送测试
✅ 模拟浏览器关闭测试

总计: 5/5 项测试通过
🎉 所有测试通过！浏览器监控功能正常工作。
```

## 配置参数

### 检测间隔
- **默认间隔**: 30秒
- **可配置**: 通过`interval`参数调整
- **建议范围**: 10-60秒

### 告警级别
- **正常关闭**: 不告警
- **异常关闭**: critical级别告警
- **连接断开**: critical级别告警

## 性能影响

### 资源消耗
- **CPU使用**: 每30秒一次检测，影响极小
- **内存使用**: 监控任务占用少量内存
- **网络使用**: 仅告警时发送消息

### 优化建议
- 根据实际需求调整检测间隔
- 在低负载时段增加检测频率
- 在高负载时段减少检测频率

## 故障排除

### 常见问题

#### 1. 误报异常关闭
**原因**: 网络延迟导致检测误判
**解决**: 增加重试机制，延长检测间隔

#### 2. 监控任务异常退出
**原因**: 页面对象失效
**解决**: 增加异常捕获，自动重启监控任务

#### 3. 告警发送失败
**原因**: 网络问题或webhook配置错误
**解决**: 检查网络连接和webhook配置

### 调试方法

#### 1. 查看日志
```bash
# 查看浏览器监控相关日志
grep "浏览器监控" logs/worker.log
```

#### 2. 手动测试
```python
# 手动测试浏览器状态检测
from src.celery_worker import check_browser_status
status = await check_browser_status(page)
print(status)
```

#### 3. 模拟异常
```python
# 手动关闭浏览器测试检测
await page.context.browser.close()
is_abnormal = await detect_browser_abnormal_closure(page)
```

## 使用说明

### 1. **自动启动**
Worker启动时会自动启动浏览器监控任务，无需手动配置。

### 2. **告警接收**
当检测到浏览器异常关闭时，会自动发送企业微信告警到配置的webhook。

### 3. **管理模式**
异常后会自动进入管理模式，等待管理员指令：
- `resume`: 恢复运行
- `restart`: 重启浏览器
- `shutdown`: 关闭Worker

### 4. **监控状态**
可以通过Redis查看Worker状态：
```bash
# 查看Worker状态
redis-cli get "worker_status:user_X"
```

## 总结

浏览器异常关闭检测功能已成功实现并集成到SRA Worker中，提供了：

✅ **实时监控**: 定期检测浏览器状态  
✅ **自动告警**: 异常时自动发送企业微信告警  
✅ **管理模式**: 异常后进入管理模式等待处理  
✅ **重启支持**: 支持通过管理指令重启浏览器  
✅ **完整测试**: 所有功能经过充分测试验证  

该功能显著提升了SRA Worker的稳定性和可观测性，能够及时发现和处理浏览器异常情况，确保系统的可靠运行。
