# SRA Worker 浏览器异常关闭检测功能

## 功能概述

SRA Worker 新增了浏览器异常关闭检测功能，能够实时监控 Playwright 浏览器状态，当检测到浏览器被异常关闭时，自动发送企业微信告警并进入管理模式等待人工干预。

## 核心功能

### 🔍 **实时状态检测**
- **页面状态检测**: 检查页面是否被关闭
- **上下文状态检测**: 检查浏览器上下文是否关闭
- **浏览器连接检测**: 检查浏览器进程连接状态
- **URL访问测试**: 通过获取当前URL验证连接性

### 🚨 **异常关闭告警**
- **自动检测**: 每30秒检测一次浏览器状态
- **分级告警**: 根据异常类型发送不同级别的告警
- **详细信息**: 包含异常原因、状态信息、建议操作
- **截图附件**: 自动附带当前页面截图

### 🔄 **自动恢复机制**
- **管理模式**: 检测到异常后自动进入管理模式
- **重启选项**: 支持通过管理指令重启浏览器
- **状态同步**: 重启后自动恢复任务处理

## 技术实现

### 检测函数

```python
async def check_browser_status(page: Page) -> dict:
    """检查浏览器状态，返回详细状态信息"""
    status_info = {
        "is_connected": False,
        "is_closed": False,
        "context_closed": False,
        "browser_closed": False,
        "page_closed": False,
        "url": "unknown",
        "error_details": ""
    }
    # ... 检测逻辑
    return status_info
```

### 异常检测函数

```python
async def detect_browser_abnormal_closure(page: Page, account_name: str = None) -> bool:
    """检测浏览器是否异常关闭"""
    status_info = await check_browser_status(page)
    
    if status_info["is_connected"]:
        return False  # 正常状态
    
    # 发送告警并返回True
    await send_monitoring_alert(...)
    return True
```

### 监控任务

```python
async def start_browser_monitor(page: Page, redis: aioredis.Redis, interval: int = 30):
    """启动浏览器监控任务"""
    async def browser_monitor_loop():
        while True:
            if await detect_browser_abnormal_closure(page):
                # 进入管理模式
                await _enter_management_mode(...)
            await asyncio.sleep(interval)
```

## 告警消息格式

### 异常关闭告警示例

```markdown
## 🚨 SRA监控告警

**告警时间**: 2024-01-15 10:30:00
**严重程度**: CRITICAL
**告警标题**: SRA Worker 浏览器异常关闭 - user_001

### 错误详情
```
SRA Worker 检测到浏览器异常关闭

时间: 2024-01-15T10:30:00
用户ID: user_001
账户: test_account

浏览器状态信息:
- 页面关闭: False
- 上下文关闭: True
- 浏览器关闭: False
- 连接状态: False
- 最后URL: https://www.zhipin.com/

错误详情: 浏览器上下文已关闭

可能的原因:
1. 浏览器进程被意外终止
2. 网络连接异常导致浏览器断开
3. 系统资源不足导致浏览器崩溃
4. 反爬虫机制触发浏览器关闭

建议操作:
1. 检查系统资源使用情况
2. 验证网络连接稳定性
3. 检查是否有反爬虫检测
4. 重启Worker服务
```
```

## 集成点

### 1. Worker启动集成
```python
# 在browser_manager()中启动监控
await start_browser_monitor(page, redis, interval=30)
```

### 2. 管理模式集成
```python
# 在_enter_management_mode中处理浏览器重启
elif command == "restart":
    new_page = await initialize_browser()
    return new_page
```

### 3. 异常处理集成
```python
# 在handle_exception_and_enter_management_mode中
await stop_browser_monitor()  # 停止监控任务
```

## 配置参数

### 检测间隔
- **默认间隔**: 30秒
- **可配置**: 通过`interval`参数调整
- **建议范围**: 10-60秒

### 告警级别
- **正常关闭**: 不告警
- **异常关闭**: critical级别告警
- **连接断开**: critical级别告警

## 测试验证

### 快速测试
```bash
# 运行浏览器监控功能测试
python tests/test_browser_monitor.py
```

### 测试项目
- ✅ 浏览器状态检测
- ✅ 异常关闭检测
- ✅ 监控任务启动/停止
- ✅ 企业微信告警发送
- ✅ 模拟浏览器关闭场景

## 故障排除

### 常见问题

#### 1. 误报异常关闭
**原因**: 网络延迟导致检测误判
**解决**: 增加重试机制，延长检测间隔

#### 2. 监控任务异常退出
**原因**: 页面对象失效
**解决**: 增加异常捕获，自动重启监控任务

#### 3. 告警发送失败
**原因**: 网络问题或webhook配置错误
**解决**: 检查网络连接和webhook配置

### 调试方法

#### 1. 查看日志
```bash
# 查看浏览器监控相关日志
grep "浏览器监控" logs/worker.log
```

#### 2. 手动测试
```python
# 手动测试浏览器状态检测
from src.celery_worker import check_browser_status
status = await check_browser_status(page)
print(status)
```

#### 3. 模拟异常
```python
# 手动关闭浏览器测试检测
await page.context.browser.close()
is_abnormal = await detect_browser_abnormal_closure(page)
```

## 性能影响

### 资源消耗
- **CPU使用**: 每30秒一次检测，影响极小
- **内存使用**: 监控任务占用少量内存
- **网络使用**: 仅告警时发送消息

### 优化建议
- 根据实际需求调整检测间隔
- 在低负载时段增加检测频率
- 在高负载时段减少检测频率

## 未来扩展

### 计划功能
1. **智能检测**: 基于历史数据优化检测算法
2. **自动重启**: 支持自动重启浏览器而不需要人工干预
3. **健康检查**: 增加更全面的浏览器健康状态检查
4. **性能监控**: 监控浏览器性能指标

### 配置增强
1. **动态间隔**: 根据系统负载动态调整检测间隔
2. **多级告警**: 支持不同级别的告警策略
3. **告警抑制**: 避免短时间内重复告警

## 总结

浏览器异常关闭检测功能为SRA Worker提供了重要的监控和告警能力，能够及时发现和处理浏览器异常情况，确保系统的稳定性和可靠性。通过企业微信告警机制，管理员能够及时了解系统状态并采取相应的处理措施。
