# 自动加载Cookie功能说明

## 功能概述

`browser_manager.py`中的`init_driver`函数现在支持自动检测并加载用户cookie文件，无需手动指定账号名或cookie文件路径。

## 主要特性

### 1. 自动检测Cookie文件
- 自动扫描`./data/`目录下的`user_*.json`格式文件
- 支持多个用户cookie文件共存
- 自动选择最新修改的cookie文件

### 2. 智能加载机制
- 验证cookie文件格式和内容有效性
- 自动设置`account_name`环境变量
- 支持启用/禁用自动加载功能

### 3. 错误处理
- 文件不存在时优雅降级
- 无效文件时跳过并继续
- 详细的日志记录

## 使用方法

### 基本使用（推荐）

```python
from src.core.browser_manager import init_driver

# 自动加载最新的cookie文件
page = await init_driver()
```

### 禁用自动加载

```python
# 禁用自动加载，使用无cookie状态
page = await init_driver(auto_load_cookies=False)
```

### 查看可用Cookie文件

```python
from src.core.browser_manager import find_available_user_cookies

# 获取所有可用的cookie文件信息
available_cookies = find_available_user_cookies()

for cookie_info in available_cookies:
    print(f"账号: {cookie_info['account_name']}")
    print(f"文件: {cookie_info['file_path']}")
    print(f"Cookie数量: {cookie_info['cookie_count']}")
    print(f"修改时间: {cookie_info['modified_time']}")
```

## Cookie文件格式

系统支持的cookie文件格式：

```json
{
  "cookies": [
    {
      "name": "cookie_name",
      "value": "cookie_value",
      "domain": ".zhipin.com",
      "path": "/",
      "expires": **********,
      "httpOnly": false,
      "secure": false,
      "sameSite": "Lax"
    }
  ]
}
```

### 必需字段
- `name`: Cookie名称
- `value`: Cookie值
- `domain`: Cookie域名

### 可选字段
- `path`: Cookie路径
- `expires`: 过期时间
- `httpOnly`: 是否仅HTTP
- `secure`: 是否安全连接
- `sameSite`: 同站策略

## 文件命名规范

Cookie文件必须遵循以下命名规范：
- 格式：`user_{账号名}.json`
- 位置：`./data/`目录下
- 示例：`./data/user_王锦程.json`

## 自动加载逻辑

1. **文件扫描**：扫描`./data/user_*.json`文件
2. **有效性验证**：检查文件格式和cookie数据
3. **文件选择**：选择最新修改的有效文件
4. **账号提取**：从文件名提取账号名
5. **Cookie加载**：将cookie添加到浏览器上下文
6. **环境设置**：设置`account_name`环境变量

## 日志输出示例

```
INFO - 正在自动加载用户 '王锦程' 的cookie文件: ./data/user_王锦程.json
INFO - ✅ 成功自动加载 11 个cookie到浏览器
INFO - 账号: 王锦程
INFO - 已设置环境变量 account_name = 王锦程
```

## 错误处理

### 无Cookie文件
```
INFO - 未找到用户cookie文件，跳过自动加载
```

### 多个文件时的选择
```
INFO - 找到多个cookie文件，选择最新的: ./data/user_王锦程.json
```

### 加载失败
```
ERROR - 自动加载cookie失败: [错误详情]
INFO - 将继续使用无cookie状态启动浏览器
```

## 兼容性

### 向后兼容
- 原有的手动cookie加载方式仍然有效
- 不影响现有的登录流程
- 可以通过参数禁用自动加载

### 环境变量
- 自动设置的`account_name`环境变量与现有系统兼容
- 其他模块可以正常使用该环境变量

## 最佳实践

### 1. 文件管理
- 定期清理过期的cookie文件
- 保持文件命名规范一致
- 备份重要的cookie文件

### 2. 安全考虑
- Cookie文件包含敏感信息，注意文件权限
- 不要将cookie文件提交到版本控制系统
- 定期更新cookie以保持有效性

### 3. 调试建议
- 使用`find_available_user_cookies()`查看可用文件
- 检查日志输出确认加载状态
- 测试时可以使用`auto_load_cookies=False`禁用自动加载

## 测试验证

运行测试脚本验证功能：

```bash
python3 test_cookie_detection.py
```

测试内容包括：
- Cookie文件检测
- 文件格式验证
- 自动加载模拟
- 错误处理测试

## 相关文件

- `src/core/browser_manager.py` - 主要实现文件
- `test_cookie_detection.py` - 功能测试脚本
- `AUTO_LOAD_COOKIES_FEATURE.md` - 本说明文档

## 总结

自动加载Cookie功能大大简化了浏览器初始化过程，用户无需手动指定cookie文件或账号名，系统会智能地选择和加载最合适的cookie文件。这个功能在保持向后兼容的同时，提供了更好的用户体验和更强的自动化能力。
